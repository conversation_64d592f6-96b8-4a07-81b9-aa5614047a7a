benchmark_name,category,timestamp,duration,throughput,latency,memory_usage,cpu_usage,gpu_utilization,success,error_message,metadata
Training Performance,training,2025-06-29T13:53:51.060021,0.14287083398085088,35324.05939670779,2.855278919450939e-05,22.1,31.1,60.0,True,,"{'iterations': 5, 'throughput_std': np.float64(3138.4209541032055), 'latency_std': np.float64(2.7461548355679185e-06), 'min_throughput': np.float64(29844.012227591407), 'max_throughput': np.float64(38498.501440022985), 'min_latency': np.float64(2.5975037016905845e-05), 'max_latency': np.float64(3.350755898281932e-05)}"
Inference Performance,inference,2025-06-29T13:53:51.153947,0.056354500993620604,44814.57349162209,2.2403503581881522e-05,22.1,16.6,60.0,True,,"{'iterations': 5, 'throughput_std': np.float64(2798.372038756403), 'latency_std': np.float64(1.4327454521522696e-06), 'min_throughput': np.float64(40377.7289247932), 'max_throughput': np.float64(47962.182686776505), 'min_latency': np.float64(2.084976003970951e-05), 'max_latency': np.float64(2.476612792816013e-05)}"
Environment Performance,environment,2025-06-29T13:53:51.248183,0.06113808503141627,81941.08950500179,1.2209683400578799e-05,22.1,7.3,60.0,True,,"{'iterations': 5, 'throughput_std': np.float64(1774.0430556702984), 'latency_std': np.float64(2.6762769190634623e-07), 'min_throughput': np.float64(79181.25290012764), 'max_throughput': np.float64(83491.0896882208), 'min_latency': np.float64(1.1977326008491218e-05), 'max_latency': np.float64(1.2629252043552696e-05)}"
MCTS Performance,mcts,2025-06-29T13:54:17.497089,25.437375028966926,17695.21187314223,0.00025437287158041726,22.6,7.7,60.0,True,,"{'iterations': 5, 'throughput_std': np.float64(13321.79175954149), 'latency_std': np.float64(0.00028925967265787785), 'min_throughput': np.float64(1321.408100581205), 'max_throughput': np.float64(33629.11631985327), 'min_latency': np.float64(2.9736136700375938e-05), 'max_latency': np.float64(0.0007567684801993892)}"
Data Loading Performance,data_loading,2025-06-29T13:54:19.387883,1.2429996560094878,32184.388260064396,3.107226239953888e-05,22.6,8.3,60.0,True,,"{'iterations': 5, 'throughput_std': np.float64(207.29418247254665), 'latency_std': np.float64(2.0144166368132925e-07), 'min_throughput': np.float64(31800.41196465561), 'max_throughput': np.float64(32367.883656814538), 'min_latency': np.float64(3.089482187351677e-05), 'max_latency': np.float64(3.144613350013969e-05)}"
