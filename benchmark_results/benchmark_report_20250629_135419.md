# 性能基准测试报告

**生成时间**: 2025-06-29T13:54:19.403174
**测试配置**: 5 次迭代，2 次预热

## 总体统计

- 总测试数: 5
- 成功测试: 5
- 成功率: 100.0%

## Environment 类别

### Environment Performance

- **吞吐量**: 81941.09 样本/秒
- **延迟**: 0.0000 秒
- **持续时间**: 0.06 秒
- **内存使用**: 22.1%
- **CPU使用**: 7.3%
- **GPU利用率**: 60.0%
- **标准差**: 吞吐量±1774.04, 延迟±0.0000

## Data_Loading 类别

### Data Loading Performance

- **吞吐量**: 32184.39 样本/秒
- **延迟**: 0.0000 秒
- **持续时间**: 1.24 秒
- **内存使用**: 22.6%
- **CPU使用**: 8.3%
- **GPU利用率**: 60.0%
- **标准差**: 吞吐量±207.29, 延迟±0.0000

## Training 类别

### Training Performance

- **吞吐量**: 35324.06 样本/秒
- **延迟**: 0.0000 秒
- **持续时间**: 0.14 秒
- **内存使用**: 22.1%
- **CPU使用**: 31.1%
- **GPU利用率**: 60.0%
- **标准差**: 吞吐量±3138.42, 延迟±0.0000

## Inference 类别

### Inference Performance

- **吞吐量**: 44814.57 样本/秒
- **延迟**: 0.0000 秒
- **持续时间**: 0.06 秒
- **内存使用**: 22.1%
- **CPU使用**: 16.6%
- **GPU利用率**: 60.0%
- **标准差**: 吞吐量±2798.37, 延迟±0.0000

## Mcts 类别

### MCTS Performance

- **吞吐量**: 17695.21 样本/秒
- **延迟**: 0.0003 秒
- **持续时间**: 25.44 秒
- **内存使用**: 22.6%
- **CPU使用**: 7.7%
- **GPU利用率**: 60.0%
- **标准差**: 吞吐量±13321.79, 延迟±0.0003

## 性能排名

### 按吞吐量排序

1. Environment Performance: 81941.09 样本/秒
2. Inference Performance: 44814.57 样本/秒
3. Training Performance: 35324.06 样本/秒
4. Data Loading Performance: 32184.39 样本/秒
5. MCTS Performance: 17695.21 样本/秒

### 按延迟排序 (低延迟优先)

1. Environment Performance: 0.0000 秒
2. Inference Performance: 0.0000 秒
3. Training Performance: 0.0000 秒
4. Data Loading Performance: 0.0000 秒
5. MCTS Performance: 0.0003 秒
