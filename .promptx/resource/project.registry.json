{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-25T14:13:48.607Z", "updatedAt": "2025-06-25T14:13:48.639Z", "resourceCount": 7}, "resources": [{"id": "ai-symbiant", "source": "project", "protocol": "role", "name": "Ai Symbiant 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ai-symbiant/ai-symbiant.role.md", "metadata": {"createdAt": "2025-06-25T14:13:48.617Z", "updatedAt": "2025-06-25T14:13:48.617Z", "scannedAt": "2025-06-25T14:13:48.617Z"}}, {"id": "dynamic-thinking", "source": "project", "protocol": "thought", "name": "Dynamic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ai-symbiant/thought/dynamic-thinking.thought.md", "metadata": {"createdAt": "2025-06-25T14:13:48.624Z", "updatedAt": "2025-06-25T14:13:48.624Z", "scannedAt": "2025-06-25T14:13:48.624Z"}}, {"id": "self-reflection", "source": "project", "protocol": "thought", "name": "Self Reflection 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ai-symbiant/thought/self-reflection.thought.md", "metadata": {"createdAt": "2025-06-25T14:13:48.624Z", "updatedAt": "2025-06-25T14:13:48.624Z", "scannedAt": "2025-06-25T14:13:48.624Z"}}, {"id": "autonomous-execution", "source": "project", "protocol": "execution", "name": "Autonomous Execution 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ai-symbiant/execution/autonomous-execution.execution.md", "metadata": {"createdAt": "2025-06-25T14:13:48.630Z", "updatedAt": "2025-06-25T14:13:48.630Z", "scannedAt": "2025-06-25T14:13:48.630Z"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ai-symbiant/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-06-25T14:13:48.630Z", "updatedAt": "2025-06-25T14:13:48.630Z", "scannedAt": "2025-06-25T14:13:48.630Z"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ai-symbiant/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-06-25T14:13:48.630Z", "updatedAt": "2025-06-25T14:13:48.630Z", "scannedAt": "2025-06-25T14:13:48.630Z"}}, {"id": "reinforcement-learning-2025", "source": "project", "protocol": "knowledge", "name": "Reinforcement Learning 2025 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ai-symbiant/knowledge/reinforcement-learning-2025.knowledge.md", "metadata": {"createdAt": "2025-06-25T14:13:48.637Z", "updatedAt": "2025-06-25T14:13:48.637Z", "scannedAt": "2025-06-25T14:13:48.637Z"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 1, "thought": 2, "execution": 3, "knowledge": 1}, "bySource": {"project": 7}}}