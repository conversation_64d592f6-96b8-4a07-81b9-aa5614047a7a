version: 4.14.0
installed_at: '2025-06-25T16:34:06.423Z'
install_type: full
agent: null
ide_setup: null
ides_setup: []
files:
  - path: .bmad-core/james.bamd.md
    hash: 0aeeb7296c29193e
    modified: false
  - path: .bmad-core/install-manifest.yml.bak2
    hash: 07be414107bf2cd1
    modified: false
  - path: .bmad-core/install-manifest.yml.bak1
    hash: b3832cb6fd165c50
    modified: false
  - path: .bmad-core/install-manifest.yml.bak
    hash: fac0dbbaaf3b6e7b
    modified: false
  - path: .bmad-core/install-manifest.yml
    hash: 07be414107bf2cd1
    modified: false
  - path: .bmad-core/core-config.yml
    hash: 8ccd25183820a4a9
    modified: false
  - path: .bmad-core/bob.bamd.md
    hash: 191e7ec538ff258c
    modified: false
  - path: .bmad-core/workflows/greenfield-ui.yml
    hash: fed0d7f34062ff95
    modified: false
  - path: .bmad-core/workflows/greenfield-service.yml
    hash: 9250ed616aecfb6c
    modified: false
  - path: .bmad-core/workflows/greenfield-fullstack.yml
    hash: 6d650ead514b3cd8
    modified: false
  - path: .bmad-core/workflows/game-prototype.yml
    hash: ddb28541f1b4aad4
    modified: false
  - path: .bmad-core/workflows/game-dev-greenfield.yml
    hash: 12cfcbc74c9a2416
    modified: false
  - path: .bmad-core/workflows/brownfield-ui.yml
    hash: 00ba2e9c095151d0
    modified: false
  - path: .bmad-core/workflows/brownfield-service.yml
    hash: 8b7748bb6010c557
    modified: false
  - path: .bmad-core/workflows/brownfield-fullstack.yml
    hash: 0317b34832a19627
    modified: false
  - path: .bmad-core/utils/workflow-management.md
    hash: 6c9da44e60ec257f
    modified: false
  - path: .bmad-core/utils/web-agent-startup-instructions.md
    hash: 27ee619b1a953aa6
    modified: false
  - path: .bmad-core/utils/template-format.md
    hash: c91208908af1dc78
    modified: false
  - path: .bmad-core/utils/file-resolution-context.md
    hash: a17fd3670e680f0c
    modified: false
  - path: .bmad-core/templates/story-tmpl.md
    hash: 3d8d98f09c3bcd3a
    modified: false
  - path: .bmad-core/templates/project-brief-tmpl.md
    hash: 4574687e30632eed
    modified: false
  - path: .bmad-core/templates/prd-tmpl.md
    hash: b5d75306c428615b
    modified: false
  - path: .bmad-core/templates/market-research-tmpl.md
    hash: 3e0481172cc9f056
    modified: false
  - path: .bmad-core/templates/level-design-doc-tmpl.md
    hash: 175ae1869c39a754
    modified: false
  - path: .bmad-core/templates/infrastructure-platform-from-arch-tmpl.md
    hash: b0a0f160b989cd68
    modified: false
  - path: .bmad-core/templates/infrastructure-architecture-tmpl.md
    hash: 1465d14985aca5cf
    modified: false
  - path: .bmad-core/templates/game-story-tmpl.md
    hash: 1e6c8afd85acaddb
    modified: false
  - path: .bmad-core/templates/game-design-doc-tmpl.md
    hash: 0a14814c66e51279
    modified: false
  - path: .bmad-core/templates/game-brief-tmpl.md
    hash: 261b7a89249347e5
    modified: false
  - path: .bmad-core/templates/game-architecture-tmpl.md
    hash: f7a9ba115dfaa3d4
    modified: false
  - path: .bmad-core/templates/fullstack-architecture-tmpl.md
    hash: 969ca155b5832956
    modified: false
  - path: .bmad-core/templates/front-end-spec-tmpl.md
    hash: fb4866eaeb8722fb
    modified: false
  - path: .bmad-core/templates/front-end-architecture-tmpl.md
    hash: 84025d1fc469d2c5
    modified: false
  - path: .bmad-core/templates/expansion-pack-plan-tmpl.md
    hash: 64e45fc281a96560
    modified: false
  - path: .bmad-core/templates/competitor-analysis-tmpl.md
    hash: d4e25586a643f8ca
    modified: false
  - path: .bmad-core/templates/brownfield-prd-tmpl.md
    hash: 3c6039fa53337ba3
    modified: false
  - path: .bmad-core/templates/brownfield-architecture-tmpl.md
    hash: c7b0962d7e856aae
    modified: false
  - path: .bmad-core/templates/architecture-tmpl.md
    hash: b8daeb43d0196ee1
    modified: false
  - path: .bmad-core/templates/agent-tmpl.md
    hash: 8ce01ebd5480ffde
    modified: false
  - path: .bmad-core/templates/agent-teams-tmpl.md
    hash: ad2f04b0b6e21633
    modified: false
  - path: .bmad-core/tasks/validate-infrastructure.md
    hash: 204348a3617ee91b
    modified: false
  - path: .bmad-core/tasks/shard-doc.md
    hash: 0a04158f420a09df
    modified: false
  - path: .bmad-core/tasks/review-story.md
    hash: cb88d2aed30afe7d
    modified: false
  - path: .bmad-core/tasks/review-infrastructure.md
    hash: 16f31fb41114cbb2
    modified: false
  - path: .bmad-core/tasks/kb-mode-interaction.md
    hash: 97623a1fbcb686e4
    modified: false
  - path: .bmad-core/tasks/index-docs.md
    hash: 349f0ddf65dd71fe
    modified: false
  - path: .bmad-core/tasks/generate-expansion-pack.md
    hash: a0494d1f3d11a637
    modified: false
  - path: .bmad-core/tasks/generate-ai-frontend-prompt.md
    hash: 1298309d4e874b0b
    modified: false
  - path: .bmad-core/tasks/game-design-brainstorming.md
    hash: fe608dd7b1cbfe82
    modified: false
  - path: .bmad-core/tasks/execute-checklist.md
    hash: e83f4ae155bb5db8
    modified: false
  - path: .bmad-core/tasks/document-project.md
    hash: 6daded58e413997d
    modified: false
  - path: .bmad-core/tasks/doc-migration-task.md
    hash: 6a86e5d9cd386452
    modified: false
  - path: .bmad-core/tasks/create-next-story.md
    hash: be7699b750a382e3
    modified: false
  - path: .bmad-core/tasks/create-game-story.md
    hash: 79548d0d9e0a41dc
    modified: false
  - path: .bmad-core/tasks/create-doc.md
    hash: 63ef4f97bfd66c1b
    modified: false
  - path: .bmad-core/tasks/create-deep-research-prompt.md
    hash: a104cb9b17cde3b7
    modified: false
  - path: .bmad-core/tasks/create-agent.md
    hash: 8abb08933ea3cfb6
    modified: false
  - path: .bmad-core/tasks/correct-course.md
    hash: 4da2f889fbc8b457
    modified: false
  - path: .bmad-core/tasks/core-dump.md
    hash: 44ac4d753c5ff573
    modified: false
  - path: .bmad-core/tasks/brownfield-create-story.md
    hash: 6e5cd0247836c4de
    modified: false
  - path: .bmad-core/tasks/brownfield-create-epic.md
    hash: 1b2b6c8b67a176ee
    modified: false
  - path: .bmad-core/tasks/brainstorming-techniques.md
    hash: 075818b207444cb2
    modified: false
  - path: .bmad-core/tasks/advanced-elicitation.md
    hash: 0950e5318989db16
    modified: false
  - path: .bmad-core/data/technical-preferences.md
    hash: 6530bed845540b0d
    modified: false
  - path: .bmad-core/data/development-guidelines.md
    hash: a4ca049daf82a096
    modified: false
  - path: .bmad-core/data/bmad-kb.md
    hash: cc192f1a55b71304
    modified: false
  - path: .bmad-core/checklists/story-draft-checklist.md
    hash: 28d5c2d3e9088320
    modified: false
  - path: .bmad-core/checklists/story-dod-checklist.md
    hash: 06ab7e73a69f930a
    modified: false
  - path: .bmad-core/checklists/po-master-checklist.md
    hash: 89d2dc785aa0e8a7
    modified: false
  - path: .bmad-core/checklists/pm-checklist.md
    hash: 139209e205a92628
    modified: false
  - path: .bmad-core/checklists/infrastructure-checklist.md
    hash: b3dc0a7ec1696c6c
    modified: false
  - path: .bmad-core/checklists/game-story-dod-checklist.md
    hash: 1c04d3dac10a357a
    modified: false
  - path: .bmad-core/checklists/game-design-checklist.md
    hash: 0f802420a3e7f7ed
    modified: false
  - path: .bmad-core/checklists/change-checklist.md
    hash: 5f2e21564d452d35
    modified: false
  - path: .bmad-core/checklists/architect-checklist.md
    hash: 99f4655b9ff99dd1
    modified: false
  - path: .bmad-core/agents/ux-expert.md
    hash: db26962b08a17c7b
    modified: false
  - path: .bmad-core/agents/sm.md
    hash: 92ff5d6215ca2dcb
    modified: false
  - path: .bmad-core/agents/qa.md
    hash: be99105e59b52422
    modified: false
  - path: .bmad-core/agents/po.md
    hash: 76d33c1721142f31
    modified: false
  - path: .bmad-core/agents/pm.md
    hash: 40f796bdefcd14c6
    modified: false
  - path: .bmad-core/agents/infra-devops-platform.md
    hash: 063294311d03ebdf
    modified: false
  - path: .bmad-core/agents/game-sm.md
    hash: f243ad5133343fdf
    modified: false
  - path: .bmad-core/agents/game-developer.md
    hash: f3ef145e8d940deb
    modified: false
  - path: .bmad-core/agents/game-designer.md
    hash: ceda694aa5939de5
    modified: false
  - path: .bmad-core/agents/dev.md
    hash: 3e65e0ce05572906
    modified: false
  - path: .bmad-core/agents/bmad-the-creator.md
    hash: 3b72c28907a0be15
    modified: false
  - path: .bmad-core/agents/bmad-orchestrator.md
    hash: c26017d6501d2e06
    modified: false
  - path: .bmad-core/agents/bmad-master.md
    hash: 1a35aa8a54c996d0
    modified: false
  - path: .bmad-core/agents/architect.md
    hash: 8c7f7244f7394781
    modified: false
  - path: .bmad-core/agents/analyst.md
    hash: f04c2ac401442edc
    modified: false
  - path: .bmad-core/agent-teams/team-no-ui.yml
    hash: db791eaa9b060ad3
    modified: false
  - path: .bmad-core/agent-teams/team-ide-minimal.yml
    hash: 600b6795116fd74e
    modified: false
  - path: .bmad-core/agent-teams/team-fullstack.yml
    hash: 8adda579fff343cd
    modified: false
  - path: .bmad-core/agent-teams/team-all.yml
    hash: 788067c0fd19c76d
    modified: false
  - path: .bmad-core/agent-teams/phaser-2d-nodejs-game-team.yml
    hash: 582f1dd4225fe54a
    modified: false
