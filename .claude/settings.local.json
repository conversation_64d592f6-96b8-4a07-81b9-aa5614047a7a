{"permissions": {"allow": ["*(*)", "*(*:*)", "Bash(*)", "Read(*)", "Edit(*)", "MultiEdit(*)", "Write(*)", "Glob(*)", "Grep(*)", "LS(*)", "NotebookRead(*)", "NotebookEdit(*)", "WebFetch(*)", "WebSearch(*)", "TodoRead(*)", "TodoWrite(*)", "Task(*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_install", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(wslview:*)", "<PERSON><PERSON>(sensible-browser:*)", "*(*:*)", "mcp__clear-thought__sequentialthinking", "Bash(ls:*)", "<PERSON><PERSON>(source:*)", "Bash(conda info:*)", "Bash(conda activate:*)", "<PERSON><PERSON>(conda init:*)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/python cardgame_ai/zhuchengxu/auto_deploy.py --show-config)", "Bash(export:*)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/pip install numpy pyyaml psutil)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/python cardgame_ai/zhuchengxu/auto_deploy.py --monitor)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/pip install -r requirements.txt)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/python cardgame_ai/zhuchengxu/auto_deploy.py --dry-run)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/python -c \"import torch; print(torch.__version__)\")", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/pip install h5py)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/python cardgame_ai/zhuchengxu/main_training.py --config auto_config_efficient_zero_doudizhu.yaml)", "Bash(/home/<USER>/miniconda3/envs/cardgame/bin/pip install lz4)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(conda list:*)", "<PERSON><PERSON>(conda run:*)", "Bash(rg:*)", "Bash(cp:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(echo $PYTHONPATH)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(pip show:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "Bash(conda:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(dos2unix:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude mcp add clear-thought -s user npx @waldzellai/clear-thought)", "<PERSON><PERSON>(claude mcp list)", "mcp__promptx__promptx_init", "mcp__promptx__promptx_hello", "mcp__promptx__promptx_recall", "mcp__promptx__promptx_action", "mcp__promptx__promptx_remember", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:raw.githubusercontent.com)", "Bash(git checkout:*)", "<PERSON><PERSON>(cat:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(claude mcp add:*)", "<PERSON>sh(xdg-open:*)", "Bash(node:*)", "Bash(npm --version)", "Bash(npm install:*)", "Bash(npx:*)", "Bash(npm init:*)", "Bash(./node_modules/.bin/playwright-mcp:*)", "Bash(./node_modules/.bin/mcp-server-playwright:*)", "<PERSON><PERSON>(timeout:*)", "mcp__promptx__promptx_welcome", "mcp__promptx__promptx_welcome", "Bash(git branch:*)", "Bash(git add:*)", "<PERSON><PERSON>(nvidia-smi:*)"], "deny": []}}