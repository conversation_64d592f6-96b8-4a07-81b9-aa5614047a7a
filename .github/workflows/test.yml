name: 测试工作流

on:
  push:
    branches: [ develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      test_type:
        description: '测试类型'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - performance
          - smoke

env:
  PYTHON_VERSION: '3.10'
  PYTEST_WORKERS: 'auto'
  COVERAGE_THRESHOLD: '80'

jobs:
  # 快速冒烟测试
  smoke-test:
    name: 冒烟测试
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'smoke' || github.event.inputs.test_type == 'all' || github.event_name != 'workflow_dispatch'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 快速安装
        run: |
          pip install pytest pytest-timeout
          pip install -e .
          
      - name: 运行冒烟测试
        run: |
          pytest tests/ -v -m smoke --timeout=180 --tb=short
          
  # 全面单元测试
  unit-test-full:
    name: 完整单元测试
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'unit' || github.event.inputs.test_type == 'all'
    needs: smoke-test
    strategy:
      matrix:
        test_group: ['algorithms', 'core', 'games', 'training', 'utils']
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          activate-environment: cardgame
          environment-file: environment.yml
          
      - name: 安装测试依赖
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-cov pytest-xdist pytest-timeout pytest-mock
          
      - name: 运行单元测试 - ${{ matrix.test_group }}
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pytest tests/unit/test_${{ matrix.test_group }}*.py -v \
            --cov=cardgame_ai.${{ matrix.test_group }} \
            --cov-report=xml:coverage-${{ matrix.test_group }}.xml \
            --cov-report=term \
            -n ${{ env.PYTEST_WORKERS }}
            
      - name: 检查覆盖率
        shell: bash -l {0}
        run: |
          conda activate cardgame
          coverage report --fail-under=${{ env.COVERAGE_THRESHOLD }}
        continue-on-error: true
        
      - name: 上传覆盖率
        uses: actions/upload-artifact@v3
        with:
          name: coverage-reports
          path: coverage-*.xml
          
  # 集成测试套件
  integration-test-suite:
    name: 集成测试套件
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'integration' || github.event.inputs.test_type == 'all'
    needs: smoke-test
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          activate-environment: cardgame
          environment-file: environment.yml
          
      - name: 安装项目
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-timeout pytest-asyncio
          
      - name: 运行集成测试
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pytest tests/integration/ -v --timeout=600 \
            --junit-xml=integration-test-results.xml
            
      - name: 发布测试结果
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          files: integration-test-results.xml
          check_name: 集成测试结果
          
  # 性能测试套件
  performance-test-suite:
    name: 性能测试套件
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'performance' || (github.event.inputs.test_type == 'all' && github.ref == 'refs/heads/main')
    needs: [unit-test-full, integration-test-suite]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          activate-environment: cardgame
          environment-file: environment.yml
          
      - name: 安装性能测试工具
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-benchmark matplotlib pandas
          
      - name: 运行性能基准
        shell: bash -l {0}
        run: |
          conda activate cardgame
          python tests/benchmarks/test_performance_benchmarks.py
          
      - name: 检查性能回归
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pytest tests/benchmarks/test_performance_regression.py -v
          
      - name: 生成性能报告
        shell: bash -l {0}
        if: always()
        run: |
          conda activate cardgame
          python tests/benchmarks/test_performance_regression.py dashboard
          
      - name: 上传性能报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-reports
          path: |
            benchmark_results/
            performance_dashboard.md
            performance_plots/
            
  # 测试报告汇总
  test-report:
    name: 测试报告汇总
    runs-on: ubuntu-latest
    needs: [smoke-test, unit-test-full, integration-test-suite]
    if: always()
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 下载所有测试结果
        uses: actions/download-artifact@v3
        
      - name: 安装报告工具
        run: |
          pip install junit2html coverage
          
      - name: 合并覆盖率报告
        run: |
          coverage combine coverage-reports/coverage-*.xml
          coverage report
          coverage html
          
      - name: 生成HTML报告
        run: |
          junit2html integration-test-results.xml integration-test-report.html
          
      - name: 创建测试摘要
        run: |
          echo "# 测试执行摘要" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 测试状态" >> $GITHUB_STEP_SUMMARY
          echo "- 冒烟测试: ${{ needs.smoke-test.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- 单元测试: ${{ needs.unit-test-full.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- 集成测试: ${{ needs.integration-test-suite.result }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 覆盖率报告" >> $GITHUB_STEP_SUMMARY
          coverage report --format=markdown >> $GITHUB_STEP_SUMMARY
          
      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: |
            htmlcov/
            integration-test-report.html
            
  # 测试矩阵验证
  test-matrix:
    name: 测试矩阵 - Python ${{ matrix.python }} - ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    if: github.ref == 'refs/heads/main' || contains(github.event.head_commit.message, '[full-test]')
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python: ['3.8', '3.9', '3.10', '3.11']
        exclude:
          # 排除一些不常用的组合以节省时间
          - os: windows-latest
            python: '3.8'
          - os: macos-latest
            python: '3.8'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python }}
          
      - name: 安装基础依赖
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-timeout
          
      - name: 安装项目（最小依赖）
        run: |
          pip install -e .
          
      - name: 运行核心测试
        run: |
          pytest tests/unit/test_core*.py -v --timeout=300