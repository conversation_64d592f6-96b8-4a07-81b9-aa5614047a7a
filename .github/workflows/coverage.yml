name: 代码覆盖率

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.10'
  MIN_COVERAGE: 80

jobs:
  coverage:
    name: 测试覆盖率分析
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整历史以便计算覆盖率变化
          
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          activate-environment: cardgame
          environment-file: environment.yml
          
      - name: 安装覆盖率工具
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-cov coverage[toml] pytest-xdist
          
      - name: 运行测试并收集覆盖率
        shell: bash -l {0}
        run: |
          conda activate cardgame
          # 运行所有测试并收集覆盖率
          pytest tests/ \
            --cov=cardgame_ai \
            --cov-branch \
            --cov-report=xml \
            --cov-report=html \
            --cov-report=term-missing:skip-covered \
            --cov-fail-under=${{ env.MIN_COVERAGE }} \
            -n auto \
            --timeout=600
            
      - name: 生成覆盖率报告
        shell: bash -l {0}
        if: always()
        run: |
          conda activate cardgame
          # 生成详细报告
          coverage report --precision=2
          
          # 生成JSON报告用于趋势分析
          coverage json
          
          # 创建覆盖率徽章
          coverage-badge -o coverage.svg -f
          
      - name: 分析覆盖率变化
        shell: bash -l {0}
        if: github.event_name == 'pull_request'
        run: |
          conda activate cardgame
          # 获取主分支的覆盖率
          git fetch origin main:main
          git checkout main
          
          # 运行主分支测试
          pytest tests/ --cov=cardgame_ai --cov-report=json:coverage-main.json -n auto --timeout=300 || true
          
          # 切回PR分支
          git checkout -
          
          # 比较覆盖率
          python scripts/compare_coverage.py coverage-main.json coverage.json > coverage-diff.md
          
      - name: 上传覆盖率到Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: true
          verbose: true
          
      - name: 上传覆盖率到Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          path-to-lcov: ./coverage.xml
          
      - name: 发布覆盖率报告
        uses: 5monkeys/cobertura-action@master
        with:
          path: coverage.xml
          minimum_coverage: ${{ env.MIN_COVERAGE }}
          fail_below_threshold: true
          show_class_names: true
          show_missing: true
          show_line: true
          show_branch: true
          
      - name: 评论PR覆盖率
        uses: py-cov-action/python-coverage-comment-action@v3
        if: github.event_name == 'pull_request'
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          MINIMUM_GREEN: 85
          MINIMUM_ORANGE: 70
          
      - name: 创建覆盖率摘要
        if: always()
        run: |
          echo "# 代码覆盖率报告" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 添加覆盖率统计
          echo "## 覆盖率统计" >> $GITHUB_STEP_SUMMARY
          coverage report --format=markdown >> $GITHUB_STEP_SUMMARY
          
          # 添加未覆盖文件
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 覆盖率最低的文件" >> $GITHUB_STEP_SUMMARY
          echo "| 文件 | 覆盖率 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|--------|" >> $GITHUB_STEP_SUMMARY
          coverage report --sort=cover --skip-covered | tail -n 20 | awk '{print "| " $1 " | " $4 " |"}' >> $GITHUB_STEP_SUMMARY
          
          # 如果有覆盖率变化，添加差异
          if [ -f coverage-diff.md ]; then
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "## 覆盖率变化" >> $GITHUB_STEP_SUMMARY
            cat coverage-diff.md >> $GITHUB_STEP_SUMMARY
          fi
          
      - name: 上传覆盖率报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: coverage-reports
          path: |
            htmlcov/
            coverage.xml
            coverage.json
            coverage.svg
            coverage-diff.md
            
      - name: 检查覆盖率门槛
        shell: bash -l {0}
        run: |
          conda activate cardgame
          # 提取总覆盖率
          COVERAGE=$(coverage report | grep TOTAL | awk '{print $4}' | sed 's/%//')
          
          echo "当前覆盖率: ${COVERAGE}%"
          echo "最低要求: ${{ env.MIN_COVERAGE }}%"
          
          # 检查是否达到门槛
          if (( $(echo "$COVERAGE < ${{ env.MIN_COVERAGE }}" | bc -l) )); then
            echo "❌ 覆盖率未达到最低要求"
            exit 1
          else
            echo "✅ 覆盖率符合要求"
          fi
          
  # 覆盖率趋势分析
  coverage-trend:
    name: 覆盖率趋势分析
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: coverage
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 下载覆盖率报告
        uses: actions/download-artifact@v3
        with:
          name: coverage-reports
          
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 安装分析工具
        run: |
          pip install matplotlib pandas plotly
          
      - name: 下载历史数据
        uses: actions/download-artifact@v3
        with:
          name: coverage-history
          path: coverage-history/
        continue-on-error: true
        
      - name: 分析覆盖率趋势
        run: |
          python scripts/analyze_coverage_trend.py \
            --current coverage.json \
            --history coverage-history/ \
            --output coverage-trend.png
            
      - name: 更新覆盖率历史
        run: |
          mkdir -p coverage-history
          cp coverage.json coverage-history/coverage-$(date +%Y%m%d-%H%M%S).json
          
      - name: 上传趋势分析
        uses: actions/upload-artifact@v3
        with:
          name: coverage-history
          path: coverage-history/
          retention-days: 90
          
      - name: 上传趋势图表
        uses: actions/upload-artifact@v3
        with:
          name: coverage-trend
          path: coverage-trend.png