name: 部署工作流

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      version:
        description: '版本号'
        required: true

env:
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 构建Docker镜像
  build-image:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: 登录到容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha
            
      - name: 构建并推送Docker镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_DATE=${{ github.event.head_commit.timestamp }}
            VCS_REF=${{ github.sha }}
            VERSION=${{ github.event.inputs.version || github.ref_name }}
            
  # 部署到Staging环境
  deploy-staging:
    name: 部署到Staging
    runs-on: ubuntu-latest
    needs: build-image
    if: github.event.inputs.environment == 'staging' || (github.event_name == 'push' && contains(github.ref, 'rc'))
    environment:
      name: staging
      url: https://staging.cardgame-ai.example.com
    steps:
      - name: 检出部署脚本
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            deploy/
            scripts/
            
      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.STAGING_SSH_KEY }}
          
      - name: 部署到Staging服务器
        env:
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_USER: ${{ secrets.STAGING_USER }}
          IMAGE_TAG: ${{ needs.build-image.outputs.image-tag }}
        run: |
          # 复制部署脚本到服务器
          scp -o StrictHostKeyChecking=no deploy/docker-compose.staging.yml ${STAGING_USER}@${STAGING_HOST}:/opt/cardgame-ai/
          
          # 执行部署
          ssh -o StrictHostKeyChecking=no ${STAGING_USER}@${STAGING_HOST} << 'EOF'
            cd /opt/cardgame-ai
            export IMAGE_TAG=${{ env.IMAGE_TAG }}
            docker-compose -f docker-compose.staging.yml pull
            docker-compose -f docker-compose.staging.yml up -d --remove-orphans
            docker system prune -f
          EOF
          
      - name: 验证部署
        run: |
          sleep 30  # 等待服务启动
          curl -f https://staging.cardgame-ai.example.com/health || exit 1
          
      - name: 运行冒烟测试
        run: |
          python scripts/smoke_test.py --env staging
          
  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build-image, deploy-staging]
    if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v'))
    environment:
      name: production
      url: https://cardgame-ai.example.com
    steps:
      - name: 检出部署脚本
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            deploy/
            scripts/
            
      - name: 设置Kubernetes配置
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBECONFIG }}" | base64 -d > $HOME/.kube/config
          
      - name: 部署到Kubernetes
        env:
          IMAGE_TAG: ${{ needs.build-image.outputs.image-tag }}
          IMAGE_DIGEST: ${{ needs.build-image.outputs.image-digest }}
        run: |
          # 更新部署配置
          kubectl set image deployment/cardgame-ai \
            cardgame-ai=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}@${{ env.IMAGE_DIGEST }} \
            -n production
            
          # 等待部署完成
          kubectl rollout status deployment/cardgame-ai -n production --timeout=10m
          
      - name: 验证部署
        run: |
          # 检查Pod状态
          kubectl get pods -n production -l app=cardgame-ai
          
          # 验证服务健康
          kubectl exec -n production deployment/cardgame-ai -- curl -f http://localhost:8080/health
          
      - name: 运行生产测试
        run: |
          python scripts/production_test.py --minimal
          
  # 发布Release
  create-release:
    name: 创建Release
    runs-on: ubuntu-latest
    needs: [build-image, deploy-production]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 生成更新日志
        id: changelog
        uses: mikepenz/release-changelog-builder-action@v4
        with:
          configurationJson: |
            {
              "template": "## 更新内容\n\n#{{CHANGELOG}}\n\n## 贡献者\n\n#{{CONTRIBUTORS}}",
              "categories": [
                {
                  "title": "## 🚀 新功能",
                  "labels": ["feature", "enhancement"]
                },
                {
                  "title": "## 🐛 Bug修复",
                  "labels": ["bug", "fix"]
                },
                {
                  "title": "## 📚 文档",
                  "labels": ["documentation"]
                },
                {
                  "title": "## 🔧 其他",
                  "labels": []
                }
              ]
            }
          
      - name: 创建GitHub Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: "dist/*"
          body: ${{ steps.changelog.outputs.changelog }}
          draft: false
          prerelease: ${{ contains(github.ref, 'rc') }}
          
  # 通知
  notify-deployment:
    name: 发送部署通知
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: 发送通知到Slack
        if: env.SLACK_WEBHOOK_URL != ''
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            部署状态通知
            环境: ${{ github.event.inputs.environment || 'production' }}
            版本: ${{ github.event.inputs.version || github.ref_name }}
            状态: ${{ job.status }}
            执行者: ${{ github.actor }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          
      - name: 发送邮件通知
        if: failure()
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.MAIL_SERVER }}
          server_port: 465
          username: ${{ secrets.MAIL_USERNAME }}
          password: ${{ secrets.MAIL_PASSWORD }}
          subject: 部署失败通知 - ${{ github.event.inputs.environment || 'production' }}
          to: ${{ secrets.MAIL_TO }}
          from: GitHub Actions
          body: |
            部署失败
            
            环境: ${{ github.event.inputs.environment || 'production' }}
            版本: ${{ github.event.inputs.version || github.ref_name }}
            仓库: ${{ github.repository }}
            提交: ${{ github.sha }}
            
            请检查部署日志: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}