name: 持续集成

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行性能基准测试
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.10'
  POETRY_VERSION: '1.5.1'
  CACHE_NUMBER: 1  # 增加此数字以重置缓存

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 缓存pip依赖
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ env.CACHE_NUMBER }}-${{ hashFiles('**/requirements*.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-${{ env.CACHE_NUMBER }}-
            
      - name: 安装代码质量工具
        run: |
          python -m pip install --upgrade pip
          pip install black flake8 mypy isort pylint
          
      - name: 运行Black格式检查
        run: black --check cardgame_ai/ tests/
        
      - name: 运行isort导入排序检查
        run: isort --check-only cardgame_ai/ tests/
        
      - name: 运行Flake8
        run: flake8 cardgame_ai/ tests/ --config .flake8
        
      - name: 运行MyPy类型检查
        run: mypy cardgame_ai/ --config-file mypy.ini
        continue-on-error: true  # 暂时允许类型检查失败
        
      - name: 运行Pylint
        run: pylint cardgame_ai/ --rcfile=.pylintrc --exit-zero
        
  # 安全扫描
  security:
    name: 安全扫描
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 运行安全扫描
        uses: pyupio/safety@v1
        with:
          api-key: ${{ secrets.SAFETY_API_KEY }}
          
      - name: 运行Bandit安全检查
        run: |
          pip install bandit
          bandit -r cardgame_ai/ -f json -o bandit-report.json
          
      - name: 上传安全报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: bandit-report.json
          
  # 单元测试
  unit-tests:
    name: 单元测试 (Python ${{ matrix.python-version }})
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11']
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ matrix.python-version }}
          activate-environment: cardgame
          environment-file: environment.yml
          auto-activate-base: false
          
      - name: 缓存Conda环境
        uses: actions/cache@v3
        with:
          path: ${{ env.CONDA }}/envs
          key: conda-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('environment.yml') }}
          
      - name: 安装项目依赖
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-cov pytest-xdist pytest-timeout
          
      - name: 运行单元测试
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pytest tests/unit/ -v --cov=cardgame_ai --cov-report=xml --cov-report=html -n auto
          
      - name: 上传覆盖率到Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
          
  # 集成测试
  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          activate-environment: cardgame
          environment-file: environment.yml
          
      - name: 安装项目
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-timeout
          
      - name: 运行集成测试
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pytest tests/integration/ -v --timeout=300 -m "not slow"
          
  # 性能测试
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: [unit-tests]
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[perf]')
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 设置Conda环境
        uses: conda-incubator/setup-miniconda@v2
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          activate-environment: cardgame
          environment-file: environment.yml
          
      - name: 安装性能测试依赖
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pip install -e .
          pip install pytest pytest-benchmark memory_profiler line_profiler
          
      - name: 下载性能基线
        uses: actions/download-artifact@v3
        with:
          name: performance-baseline
          path: ./
        continue-on-error: true
        
      - name: 运行性能基准测试
        shell: bash -l {0}
        run: |
          conda activate cardgame
          python tests/benchmarks/test_performance_benchmarks.py
          
      - name: 运行性能回归测试
        shell: bash -l {0}
        run: |
          conda activate cardgame
          pytest tests/benchmarks/test_performance_regression.py -v
          
      - name: 生成性能报告
        shell: bash -l {0}
        run: |
          conda activate cardgame
          python tests/benchmarks/test_performance_regression.py dashboard
          
      - name: 上传性能结果
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: |
            benchmark_results/
            performance_dashboard.md
            performance_plots/
            
      - name: 更新性能基线
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v3
        with:
          name: performance-baseline
          path: performance_baseline.json
          
  # GPU测试（自托管运行器）
  gpu-tests:
    name: GPU测试
    runs-on: [self-hosted, gpu]
    needs: [unit-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 运行GPU测试
        run: |
          source ~/miniconda3/etc/profile.d/conda.sh
          conda activate cardgame
          pytest tests/gpu/ -v -m gpu
          
      - name: 验证GPU利用率
        run: |
          python scripts/verify_gpu_utilization.py
          
  # 文档构建
  documentation:
    name: 文档构建
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 安装文档工具
        run: |
          pip install sphinx sphinx-rtd-theme myst-parser
          
      - name: 构建文档
        run: |
          cd docs
          make html
          
      - name: 上传文档
        uses: actions/upload-artifact@v3
        with:
          name: documentation
          path: docs/_build/html/
          
  # 构建和发布
  build:
    name: 构建发行版
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: 安装构建工具
        run: |
          pip install build twine
          
      - name: 构建包
        run: python -m build
        
      - name: 检查包
        run: twine check dist/*
        
      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/
          
      - name: 发布到TestPyPI
        if: contains(github.ref, 'rc')
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.TEST_PYPI_TOKEN }}
        run: |
          twine upload --repository testpypi dist/*
          
      - name: 发布到PyPI
        if: '!contains(github.ref, 'rc')'
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: |
          twine upload dist/*
          
  # 通知
  notify:
    name: 发送通知
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests]
    if: always()
    steps:
      - name: 发送Slack通知
        if: env.SLACK_WEBHOOK_URL != ''
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            仓库: ${{ github.repository }}
            分支: ${{ github.ref }}
            提交: ${{ github.sha }}
            作者: ${{ github.actor }}
            状态: ${{ job.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}