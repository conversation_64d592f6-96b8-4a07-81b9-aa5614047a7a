name: 依赖管理

on:
  schedule:
    # 每周一早上8点运行
    - cron: '0 8 * * 1'
  workflow_dispatch:

jobs:
  # 更新依赖
  update-dependencies:
    name: 更新Python依赖
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
          
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: 安装依赖管理工具
        run: |
          pip install pip-tools safety pip-audit
          
      - name: 更新requirements文件
        run: |
          # 更新requirements.txt
          pip-compile requirements.in --upgrade
          
          # 更新dev requirements
          pip-compile requirements-dev.in --upgrade
          
      - name: 安全检查
        id: safety-check
        run: |
          # 检查已知漏洞
          safety check --json > safety-report.json || true
          
          # 审计依赖
          pip-audit --desc > audit-report.txt || true
          
          # 提取问题数量
          VULNERABILITIES=$(cat safety-report.json | jq '.vulnerabilities | length')
          echo "vulnerabilities=$VULNERABILITIES" >> $GITHUB_OUTPUT
          
      - name: 创建更新报告
        run: |
          echo "# 依赖更新报告" > dependency-update-report.md
          echo "" >> dependency-update-report.md
          echo "生成时间: $(date)" >> dependency-update-report.md
          echo "" >> dependency-update-report.md
          
          # 添加更新的包
          echo "## 更新的包" >> dependency-update-report.md
          git diff requirements*.txt | grep '^[+-]' | grep -v '^[+-]#' >> dependency-update-report.md || echo "无更新" >> dependency-update-report.md
          
          # 添加安全报告
          echo "" >> dependency-update-report.md
          echo "## 安全报告" >> dependency-update-report.md
          echo "发现 ${{ steps.safety-check.outputs.vulnerabilities }} 个安全漏洞" >> dependency-update-report.md
          echo "" >> dependency-update-report.md
          cat audit-report.txt >> dependency-update-report.md
          
      - name: 创建Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
          commit-message: 'chore: 更新Python依赖'
          title: '🔄 更新Python依赖'
          body-path: dependency-update-report.md
          branch: update/python-dependencies
          delete-branch: true
          labels: |
            dependencies
            automated
            
  # 更新GitHub Actions
  update-actions:
    name: 更新GitHub Actions
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
          
      - name: 更新GitHub Actions
        uses: technote-space/create-pr-action@v2
        with:
          GITHUB_TOKEN: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
          EXECUTE_COMMANDS: |
            npx -y pin-github-action@latest .github/workflows/*.yml
          COMMIT_MESSAGE: 'chore: 更新GitHub Actions到最新版本'
          PR_BRANCH_NAME: 'update/github-actions'
          PR_TITLE: '🔄 更新GitHub Actions'
          
  # 检查过时依赖
  check-outdated:
    name: 检查过时依赖
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: 检查过时的包
        run: |
          pip install pip-review
          
          # 列出所有过时的包
          pip-review --local > outdated-packages.txt
          
          # 创建报告
          echo "# 过时依赖报告" > outdated-report.md
          echo "" >> outdated-report.md
          echo "以下包有新版本可用：" >> outdated-report.md
          echo "" >> outdated-report.md
          echo '```' >> outdated-report.md
          cat outdated-packages.txt >> outdated-report.md
          echo '```' >> outdated-report.md
          
      - name: 上传报告
        uses: actions/upload-artifact@v3
        with:
          name: dependency-reports
          path: |
            outdated-report.md
            safety-report.json
            audit-report.txt
            
  # 依赖许可证检查
  license-check:
    name: 许可证合规检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: 安装许可证检查工具
        run: |
          pip install pip-licenses
          
      - name: 生成许可证报告
        run: |
          # 生成许可证列表
          pip-licenses --format=markdown --with-urls --with-description > licenses.md
          
          # 检查不兼容的许可证
          pip-licenses --fail-on="GPL;LGPL" || echo "警告: 发现GPL/LGPL许可证"
          
          # 创建摘要
          echo "# 许可证合规报告" > license-summary.md
          echo "" >> license-summary.md
          echo "## 许可证统计" >> license-summary.md
          pip-licenses --summary >> license-summary.md
          echo "" >> license-summary.md
          echo "## 需要注意的许可证" >> license-summary.md
          pip-licenses | grep -E "(GPL|LGPL|AGPL)" >> license-summary.md || echo "无需要特别注意的许可证" >> license-summary.md
          
      - name: 上传许可证报告
        uses: actions/upload-artifact@v3
        with:
          name: license-report
          path: |
            licenses.md
            license-summary.md
            
  # 创建依赖关系图
  dependency-graph:
    name: 生成依赖关系图
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: 安装项目和工具
        run: |
          pip install -e .
          pip install pipdeptree graphviz
          
      - name: 生成依赖树
        run: |
          # 生成文本格式的依赖树
          pipdeptree > dependency-tree.txt
          
          # 生成JSON格式
          pipdeptree --json > dependency-tree.json
          
          # 生成图形（如果可能）
          pipdeptree --graph-output png > dependency-graph.png || true
          
      - name: 检查循环依赖
        run: |
          # 检查循环依赖
          pipdeptree --warn fail > circular-deps.txt 2>&1 || echo "发现循环依赖"
          
      - name: 上传依赖图
        uses: actions/upload-artifact@v3
        with:
          name: dependency-graph
          path: |
            dependency-tree.txt
            dependency-tree.json
            dependency-graph.png
            circular-deps.txt