#\!/usr/bin/env python3
"""
训练进度监控脚本
实时监控训练状态，直到产生第一个模型文件
"""

import os
import time
import psutil
import subprocess
from datetime import datetime
from pathlib import Path

class TrainingProgressMonitor:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.checkpoints_dir = self.project_root / "checkpoints"
        self.logs_dir = self.project_root / "logs"
        self.mcts_logs_dir = self.logs_dir / "mcts"
        
        self.start_time = datetime.now()
        self.last_mcts_count = 0
        self.model_files_found = []
        
    def check_training_processes(self):
        """检查训练相关进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline for keyword in ['auto_deploy', 'optimized_main_training', 'multiprocessing.spawn']):
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                        'cpu_percent': proc.info['cpu_percent'],
                        'memory_percent': proc.info['memory_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def check_model_files(self):
        """检查模型文件"""
        if not self.checkpoints_dir.exists():
            return []
        
        model_files = []
        for file_path in self.checkpoints_dir.glob("*.pth"):
            stat = file_path.stat()
            model_files.append({
                'name': file_path.name,
                'size': stat.st_size,
                'mtime': datetime.fromtimestamp(stat.st_mtime)
            })
        return model_files
    
    def count_mcts_logs(self):
        """统计MCTS日志文件数量"""
        if not self.mcts_logs_dir.exists():
            return 0
        return len(list(self.mcts_logs_dir.glob("unified_mcts_*.log")))
    
    def get_recent_mcts_activity(self):
        """获取最近的MCTS活动"""
        if not self.mcts_logs_dir.exists():
            return []
        
        recent_files = []
        cutoff_time = time.time() - 300  # 最近5分钟
        
        for log_file in self.mcts_logs_dir.glob("unified_mcts_*.log"):
            if log_file.stat().st_mtime > cutoff_time:
                recent_files.append({
                    'name': log_file.name,
                    'size': log_file.stat().st_size,
                    'mtime': datetime.fromtimestamp(log_file.stat().st_mtime)
                })
        
        return sorted(recent_files, key=lambda x: x['mtime'], reverse=True)
    
    def search_for_game_progress(self):
        """搜索游戏进度信息"""
        try:
            # 搜索最新的游戏进度信息
            result = subprocess.run([
                'grep', '-r', '第.*局', str(self.logs_dir)
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                return lines[-5:] if lines else []  # 返回最后5行
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            pass
        
        return []
    
    def print_status_report(self):
        """打印状态报告"""
        now = datetime.now()
        elapsed = now - self.start_time
        
        print(f"\n{'='*80}")
        print(f"🕒 监控时间: {now.strftime('%Y-%m-%d %H:%M:%S')} (运行 {elapsed})")
        print(f"{'='*80}")
        
        # 1. 检查训练进程
        processes = self.check_training_processes()
        print(f"🔄 训练进程状态 ({len(processes)} 个进程):")
        if processes:
            for proc in processes[:5]:  # 只显示前5个
                print(f"  PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}%  < /dev/null |  "
                      f"内存 {proc['memory_percent']:.1f}% | {proc['cmdline']}")
        else:
            print("  ❌ 没有检测到训练进程")
        
        # 2. 检查模型文件
        model_files = self.check_model_files()
        print(f"\n📁 模型文件状态:")
        if model_files:
            print(f"  ✅ 发现 {len(model_files)} 个模型文件:")
            for model in model_files:
                print(f"    {model['name']} | {model['size']/1024/1024:.1f}MB | {model['mtime']}")
        else:
            print("  ⏳ 尚未发现模型文件 (每50局保存一次)")
        
        # 3. MCTS活动
        mcts_count = self.count_mcts_logs()
        recent_mcts = self.get_recent_mcts_activity()
        print(f"\n🧠 MCTS搜索活动:")
        print(f"  总MCTS日志文件: {mcts_count} 个")
        print(f"  最近5分钟活跃: {len(recent_mcts)} 个文件")
        if recent_mcts:
            print(f"  最新更新: {recent_mcts[0]['mtime']} ({recent_mcts[0]['name']})")
        
        # 4. 游戏进度
        game_progress = self.search_for_game_progress()
        print(f"\n🎮 游戏进度信息:")
        if game_progress:
            print(f"  最新进度记录 (最后5条):")
            for line in game_progress:
                if '第' in line and '局' in line:
                    # 提取时间戳和局数信息
                    parts = line.split(' - ')
                    if len(parts) >= 3:
                        timestamp = parts[0].split(':')[-1] if ':' in parts[0] else parts[0]
                        content = parts[-1]
                        print(f"    {timestamp}: {content[:100]}...")
        else:
            print("  ⚠️  未发现最新的游戏进度日志")
        
        # 5. 估算状态
        print(f"\n📊 状态估算:")
        if model_files:
            print("  🎯 任务状态: 模型文件已生成 - 准备验证\!")
            return True
        elif processes and recent_mcts:
            print("  🚀 任务状态: 训练正在进行中...")
            if mcts_count > self.last_mcts_count:
                print(f"    MCTS活动增加了 {mcts_count - self.last_mcts_count} 个文件")
                self.last_mcts_count = mcts_count
        else:
            print("  ❌ 任务状态: 训练可能已停止")
        
        return False
    
    def monitor_loop(self, check_interval=30):
        """主监控循环"""
        print("🎯 开始监控训练进度，直到产生第一个模型文件...")
        print("📋 监控目标:")
        print("  1. 训练进程状态")
        print("  2. 模型文件生成 (每50局保存)")
        print("  3. MCTS搜索活动")
        print("  4. 游戏进度记录")
        print(f"  5. 每 {check_interval} 秒检查一次")
        
        iteration = 0
        while True:
            iteration += 1
            
            # 打印状态报告
            model_found = self.print_status_report()
            
            if model_found:
                print(f"\n🎉 成功！检测到模型文件生成")
                print(f"🎯 监控任务完成，总共运行了 {iteration} 次检查")
                break
            
            print(f"\n⏱️  等待 {check_interval} 秒后进行下一次检查... (第 {iteration} 次)")
            try:
                time.sleep(check_interval)
            except KeyboardInterrupt:
                print(f"\n⚠️  监控被用户中断")
                break

if __name__ == "__main__":
    monitor = TrainingProgressMonitor()
    monitor.monitor_loop(check_interval=30)
