"""
EfficientZero总配置类

整合所有配置类，提供统一的配置管理接口。
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import yaml
import json
from pathlib import Path

from .model_config import ModelConfig
from .mcts_config import MCTSConfig
from .loss_config import LossConfig
from .training_config import TrainingConfig

@dataclass
class EfficientZeroConfig:
    """
    EfficientZero总配置类
    
    整合所有子配置，提供统一的配置管理接口。
    这个类替代了原来EfficientZero构造函数中的大量参数。
    """
    
    # 子配置
    model: ModelConfig
    mcts: MCTSConfig
    loss: LossConfig
    training: TrainingConfig
    
    # 全局配置
    experiment_name: str = "efficient_zero_experiment"
    seed: Optional[int] = None
    debug_mode: bool = False
    
    # 可选组件配置
    use_ewc: bool = False
    ewc_lambda: float = 100.0
    ewc_state_path: Optional[str] = None
    
    use_opponent_switcher: bool = False
    opponent_switcher_config: Optional[Dict[str, Any]] = None
    
    use_key_moment_detector: bool = False
    key_moment_detector_config: Optional[Dict[str, Any]] = None
    
    use_dynamic_budget_allocator: bool = False
    dynamic_budget_allocator_config: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """后处理验证"""
        # 验证子配置
        if not self.model.validate():
            raise ValueError("模型配置无效")
        
        if not self.mcts.validate():
            raise ValueError("MCTS配置无效")
        
        if not self.loss.validate():
            raise ValueError("损失配置无效")
        
        if not self.training.validate():
            raise ValueError("训练配置无效")
        
        # 验证配置一致性
        self._validate_consistency()
        
        # 设置默认的可选组件配置
        if self.opponent_switcher_config is None:
            self.opponent_switcher_config = {}
        
        if self.key_moment_detector_config is None:
            self.key_moment_detector_config = {}
        
        if self.dynamic_budget_allocator_config is None:
            self.dynamic_budget_allocator_config = {}
    
    @property
    def network(self):
        """
        提供 network 属性作为 model 的别名，以保持与其他配置系统的兼容性
        
        Returns:
            ModelConfig: 模型配置对象
        """
        return self.model
    
    def _validate_consistency(self):
        """验证配置之间的一致性"""
        # 检查动作空间大小一致性
        if hasattr(self.model, 'action_space_size'):
            # 这里可以添加与环境动作空间的一致性检查
            pass
        
        # 检查设备一致性
        if self.model.device and self.training.use_distributed:
            # 分布式训练时设备配置的一致性检查
            pass
        
        # 检查混合精度训练一致性
        if self.training.use_mixed_precision and self.loss.loss_scaling:
            # 确保混合精度和损失缩放配置一致
            pass
    
    @classmethod
    def create_default(cls) -> 'EfficientZeroConfig':
        """创建默认配置"""
        return cls(
            model=ModelConfig.create_default(),
            mcts=MCTSConfig.create_default(),
            loss=LossConfig.create_default(),
            training=TrainingConfig.create_default()
        )
    
    @classmethod
    def create_fast(cls) -> 'EfficientZeroConfig':
        """创建快速配置（用于测试）"""
        return cls(
            model=ModelConfig.create_lightweight(),
            mcts=MCTSConfig.create_fast(),
            loss=LossConfig.create_lightweight(),
            training=TrainingConfig.create_fast(),
            experiment_name="fast_test",
            debug_mode=True
        )
    
    @classmethod
    def create_production(cls) -> 'EfficientZeroConfig':
        """创建生产环境配置"""
        return cls(
            model=ModelConfig.create_high_performance(),
            mcts=MCTSConfig.create_high_quality(),
            loss=LossConfig.create_robust(),
            training=TrainingConfig.create_production(),
            experiment_name="production_training",
            use_ewc=True,
            use_opponent_switcher=True,
            use_key_moment_detector=True,
            use_dynamic_budget_allocator=True
        )
    
    @classmethod
    def create_debug(cls) -> 'EfficientZeroConfig':
        """创建调试配置"""
        return cls(
            model=ModelConfig.create_lightweight(),
            mcts=MCTSConfig.create_debug(),
            loss=LossConfig.create_lightweight(),
            training=TrainingConfig.create_fast(),
            experiment_name="debug_session",
            debug_mode=True
        )
    
    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'EfficientZeroConfig':
        """
        从YAML文件加载配置
        
        Args:
            yaml_path: YAML文件路径
            
        Returns:
            EfficientZeroConfig: 配置实例
        """
        with open(yaml_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        return cls.from_dict(config_dict)
    
    @classmethod
    def from_json(cls, json_path: str) -> 'EfficientZeroConfig':
        """
        从JSON文件加载配置
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            EfficientZeroConfig: 配置实例
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        return cls.from_dict(config_dict)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'EfficientZeroConfig':
        """
        从字典创建配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            EfficientZeroConfig: 配置实例
        """
        # 提取子配置
        model_config = ModelConfig.from_dict(config_dict.get('model', {}))
        mcts_config = MCTSConfig.from_dict(config_dict.get('mcts', {}))
        loss_config = LossConfig.from_dict(config_dict.get('loss', {}))
        training_config = TrainingConfig.from_dict(config_dict.get('training', {}))
        
        # 提取其他配置
        other_config = {k: v for k, v in config_dict.items() 
                       if k not in ['model', 'mcts', 'loss', 'training']}
        
        return cls(
            model=model_config,
            mcts=mcts_config,
            loss=loss_config,
            training=training_config,
            **other_config
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model': self.model.to_dict(),
            'mcts': self.mcts.to_dict(),
            'loss': self.loss.to_dict(),
            'training': self.training.to_dict(),
            'experiment_name': self.experiment_name,
            'seed': self.seed,
            'debug_mode': self.debug_mode,
            'use_ewc': self.use_ewc,
            'ewc_lambda': self.ewc_lambda,
            'ewc_state_path': self.ewc_state_path,
            'use_opponent_switcher': self.use_opponent_switcher,
            'opponent_switcher_config': self.opponent_switcher_config,
            'use_key_moment_detector': self.use_key_moment_detector,
            'key_moment_detector_config': self.key_moment_detector_config,
            'use_dynamic_budget_allocator': self.use_dynamic_budget_allocator,
            'dynamic_budget_allocator_config': self.dynamic_budget_allocator_config
        }
    
    def save_yaml(self, yaml_path: str):
        """
        保存配置到YAML文件
        
        Args:
            yaml_path: YAML文件路径
        """
        config_dict = self.to_dict()
        
        # 确保目录存在
        Path(yaml_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
    
    def save_json(self, json_path: str):
        """
        保存配置到JSON文件
        
        Args:
            json_path: JSON文件路径
        """
        config_dict = self.to_dict()
        
        # 确保目录存在
        Path(json_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def validate(self) -> bool:
        """
        验证整个配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 重新触发__post_init__验证
            self.__post_init__()
            return True
        except ValueError:
            return False
    
    def get_component_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        获取可选组件的配置
        
        Returns:
            Dict[str, Dict[str, Any]]: 组件配置字典
        """
        component_configs = {}
        
        if self.use_ewc:
            component_configs['ewc'] = {
                'lambda': self.ewc_lambda,
                'state_path': self.ewc_state_path
            }
        
        if self.use_opponent_switcher:
            component_configs['opponent_switcher'] = self.opponent_switcher_config
        
        if self.use_key_moment_detector:
            component_configs['key_moment_detector'] = self.key_moment_detector_config
        
        if self.use_dynamic_budget_allocator:
            component_configs['dynamic_budget_allocator'] = self.dynamic_budget_allocator_config
        
        return component_configs
    
    def update_for_environment(self, env_info: Dict[str, Any]):
        """
        根据环境信息更新配置
        
        Args:
            env_info: 环境信息字典，包含状态空间、动作空间等信息
        """
        if 'state_shape' in env_info:
            self.model.state_shape = env_info['state_shape']
        
        if 'action_space_size' in env_info:
            self.model.action_space_size = env_info['action_space_size']
        
        # 重新验证配置
        self._validate_consistency()
