"""
模型配置类

定义EfficientZero算法中模型相关的配置参数。
"""

from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class ModelConfig:
    """
    模型配置类
    
    包含神经网络模型的所有配置参数，包括网络结构、维度设置等。
    """
    
    # 基础网络配置
    state_shape: Tuple[int, ...] = (656,)  # 状态空间形状
    action_space_size: int = 565  # 动作空间大小（斗地主实际动作数）
    hidden_dim: int = 256  # 隐藏层维度
    state_dim: int = 64  # 状态表示维度
    use_resnet: bool = True  # 是否使用ResNet架构
    
    # 自监督学习配置
    projection_dim: int = 128  # 自监督学习投影维度
    prediction_dim: int = 64  # 自监督学习预测维度
    
    # 值前缀配置
    value_prefix_length: int = 5  # 值前缀长度
    
    # 分布式价值网络配置
    use_distributional_value: bool = False  # 是否使用分布式价值头
    value_support_size: int = 601  # 分布式价值支持大小
    value_min: float = -300  # 价值范围最小值
    value_max: float = 300  # 价值范围最大值
    
    # 风险敏感强化学习配置
    risk_alpha: float = 0.05  # CVaR的置信水平
    risk_beta: float = 0.1  # 风险厌恶系数
    
    # 信念状态配置
    use_belief_state: bool = False  # 是否使用信念状态
    belief_dim: int = 128  # 信念状态维度
    use_belief_attention: bool = True  # 是否使用注意力机制处理信念
    belief_attention_heads: int = 4  # 注意力头数
    use_residual_belief: bool = True  # 是否使用残差连接
    use_gating_mechanism: bool = True  # 是否使用门控机制
    
    # 设备配置
    device: Optional[str] = None  # 设备类型，None表示自动选择
    
    # 兼容性属性（与 NetworkConfig 兼容）
    # 这些属性作为 hidden_dim 的别名或提供默认值
    @property
    def hidden_size(self) -> int:
        """hidden_dim 的别名，与 NetworkConfig 兼容"""
        return self.hidden_dim
    
    @hidden_size.setter
    def hidden_size(self, value: int):
        """设置 hidden_size，实际修改 hidden_dim"""
        self.hidden_dim = value
    
    @property
    def blocks(self) -> int:
        """ResNet 块数量（默认值）"""
        return getattr(self, '_blocks', 16)
    
    @blocks.setter
    def blocks(self, value: int):
        """设置 ResNet 块数量"""
        self._blocks = value
    
    @property
    def transformer_layers(self) -> int:
        """Transformer 层数（默认值）"""
        return getattr(self, '_transformer_layers', 3)
    
    @transformer_layers.setter
    def transformer_layers(self, value: int):
        """设置 Transformer 层数"""
        self._transformer_layers = value
    
    @property
    def transformer_heads(self) -> int:
        """Transformer 注意力头数（默认值）"""
        return getattr(self, '_transformer_heads', 8)
    
    @transformer_heads.setter
    def transformer_heads(self, value: int):
        """设置 Transformer 注意力头数"""
        self._transformer_heads = value
    
    def __post_init__(self):
        """后处理验证"""
        if self.action_space_size <= 0:
            raise ValueError("动作空间大小必须大于0")
        
        if self.hidden_dim <= 0:
            raise ValueError("隐藏层维度必须大于0")
        
        if self.state_dim <= 0:
            raise ValueError("状态表示维度必须大于0")
        
        if self.use_distributional_value:
            if self.value_support_size <= 0:
                raise ValueError("分布式价值支持大小必须大于0")
            if self.value_min >= self.value_max:
                raise ValueError("价值范围最小值必须小于最大值")
        
        if self.use_belief_state:
            if self.belief_dim <= 0:
                raise ValueError("信念状态维度必须大于0")
            if self.belief_attention_heads <= 0:
                raise ValueError("注意力头数必须大于0")
    
    @classmethod
    def create_default(cls) -> 'ModelConfig':
        """创建默认配置"""
        return cls()
    
    @classmethod
    def create_lightweight(cls) -> 'ModelConfig':
        """创建轻量级配置（用于快速测试）"""
        return cls(
            hidden_dim=128,
            state_dim=32,
            projection_dim=64,
            prediction_dim=32,
            value_prefix_length=3,
            belief_dim=64,
            belief_attention_heads=2
        )
    
    @classmethod
    def create_high_performance(cls) -> 'ModelConfig':
        """创建高性能配置（用于生产环境）"""
        return cls(
            hidden_dim=512,
            state_dim=128,
            projection_dim=256,
            prediction_dim=128,
            value_prefix_length=10,
            use_distributional_value=True,
            use_belief_state=True,
            belief_dim=256,
            belief_attention_heads=8
        )
    
    def validate(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 重新触发__post_init__验证
            self.__post_init__()
            return True
        except ValueError:
            return False
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'state_shape': self.state_shape,
            'action_space_size': self.action_space_size,
            'hidden_dim': self.hidden_dim,
            'state_dim': self.state_dim,
            'use_resnet': self.use_resnet,
            'projection_dim': self.projection_dim,
            'prediction_dim': self.prediction_dim,
            'value_prefix_length': self.value_prefix_length,
            'use_distributional_value': self.use_distributional_value,
            'value_support_size': self.value_support_size,
            'value_min': self.value_min,
            'value_max': self.value_max,
            'risk_alpha': self.risk_alpha,
            'risk_beta': self.risk_beta,
            'use_belief_state': self.use_belief_state,
            'belief_dim': self.belief_dim,
            'use_belief_attention': self.use_belief_attention,
            'belief_attention_heads': self.belief_attention_heads,
            'use_residual_belief': self.use_residual_belief,
            'use_gating_mechanism': self.use_gating_mechanism,
            'device': self.device
        }
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'ModelConfig':
        """从字典创建配置"""
        return cls(**config_dict)
