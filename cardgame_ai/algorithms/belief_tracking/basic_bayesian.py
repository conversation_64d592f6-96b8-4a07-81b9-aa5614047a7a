"""
基础贝叶斯信念追踪器模块

实现基于简单规则和已知信息（公共牌、出牌历史）更新对手手牌概率分布的基础版本。
"""
from typing import Dict, List, Set, Optional, Any
import time
from cardgame_ai.utils.logging import get_logger
# import logging  # 保留以备向后兼容
from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type

# 配置日志
logger = get_logger(__name__)

class BayesianBeliefTracker:
    """
    基础贝叶斯信念追踪器

    基于简单规则和已知信息（公共牌、出牌历史）更新对手手牌概率分布。
    """

    def __init__(
        self,
        player_id: str,
        initial_known_cards: Set[Card],
        all_possible_cards: Set[Card],
        initial_hand_size: Optional[int] = None
    ):
        """
        初始化贝叶斯信念追踪器

        Args:
            player_id (str): 被追踪的玩家ID
            initial_known_cards (Set[Card]): 初始已知的牌（如公共牌、自己的手牌）
            all_possible_cards (Set[Card]): 所有可能的牌
            initial_hand_size (Optional[int], optional): 初始手牌数量. Defaults to None.
        """
        # 转换卡牌为字符串表示
        initial_known_cards_str = {str(card) for card in initial_known_cards}
        all_possible_cards_str = {str(card) for card in all_possible_cards}

        # 创建初始信念状态
        excluded_cards = initial_known_cards_str  # 已知的牌不可能在对手手中
        self.belief = BeliefState.create_uniform(
            player_id=player_id,
            all_cards=list(all_possible_cards_str),
            excluded_cards=list(excluded_cards)
        )

        # 设置估计的手牌数量
        self.belief.estimated_hand_length = initial_hand_size

        # 记录历史信息
        self.history: List[Dict[str, Any]] = []
        self.last_update_time = time.time()

    def update(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any]
    ) -> None:
        """
        根据对手的动作和公共信息更新信念状态

        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作，如果是None表示对手选择不出牌
            public_knowledge (Dict[str, Any]): 公共信息，包括已知的公共牌、历史出牌等
        """
        current_time = time.time()

        # 记录更新历史
        update_record = {
            'time': current_time,
            'action': None if opponent_action is None else [str(card) for card in opponent_action],
            'public_knowledge': public_knowledge
        }
        self.history.append(update_record)

        # 更新信念状态
        new_probs = self.belief.card_probabilities.copy()

        # 如果对手出牌了，更新相应的概率
        if opponent_action is not None and len(opponent_action) > 0:
            # 对手出的牌，概率为1.0（确定在对手手中）
            action_cards = [str(card) for card in opponent_action]
            for card in action_cards:
                new_probs[card] = 0.0  # 已经打出的牌，概率变为0

            # 更新手牌数量估计
            if self.belief.estimated_hand_length is not None:
                self.belief.estimated_hand_length -= len(action_cards)

        # 如果对手选择不出牌（Pass），根据上家出牌情况更新概率
        elif opponent_action is None and 'last_play' in public_knowledge:
            last_play = public_knowledge.get('last_play')
            if last_play is not None:
                # 获取上家出牌的牌型
                last_cards = [Card.from_string(card) if isinstance(card, str) else card
                             for card in last_play]
                last_card_group = CardGroup(last_cards)
                last_card_type = get_card_group_type(last_card_group)

                # 如果上家出的是大牌（如炸弹、火箭），增加对手没有更大牌的概率
                if last_card_type in [CardGroupType.BOMB, CardGroupType.ROCKET]:
                    self._update_pass_after_bomb(new_probs, last_card_group)
                else:
                    self._update_pass_after_normal_play(new_probs, last_card_group)

        # 更新公共信息中的已知牌
        if 'known_cards' in public_knowledge:
            known_cards = public_knowledge['known_cards']
            if isinstance(known_cards, list) and len(known_cards) > 0:
                known_cards_str = [str(card) if not isinstance(card, str) else card
                                  for card in known_cards]
                for card in known_cards_str:
                    new_probs[card] = 0.0  # 已知的公共牌，对手不可能持有

        # 应用概率更新
        self.belief.update_probabilities(new_probs)
        self.belief.normalize_probabilities()

        # 更新元数据
        self.belief.source = BeliefSource.INFERENCE
        self.belief.last_updated = current_time
        self.belief.version += 1
        self.last_update_time = current_time

    def _update_pass_after_bomb(self, probs: Dict[str, float], bomb_group: CardGroup) -> None:
        """
        在对手面对炸弹选择不出牌时更新概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            bomb_group (CardGroup): 炸弹牌组
        """
        # 降低对手有更大炸弹的概率
        bomb_rank = bomb_group.main_rank

        # 遍历所有可能的炸弹
        for rank in range(int(bomb_rank) + 1, int(CardRank.BIG_JOKER) + 1):
            # 对于比当前炸弹大的牌，降低概率
            for suit in range(int(CardSuit.CLUB), int(CardSuit.SPADE) + 1):
                card_str = str(Card(CardRank(rank), CardSuit(suit)))
                if card_str in probs:
                    probs[card_str] *= 0.3  # 大幅降低概率

        # 特别处理王炸
        if bomb_group.type != CardGroupType.ROCKET:
            small_joker = str(Card(CardRank.SMALL_JOKER))
            big_joker = str(Card(CardRank.BIG_JOKER))

            if small_joker in probs and big_joker in probs:
                # 如果两张王都有较高概率，则同时降低它们的概率
                if probs[small_joker] > 0.5 and probs[big_joker] > 0.5:
                    probs[small_joker] *= 0.2
                    probs[big_joker] *= 0.2

    def _update_pass_after_normal_play(self, probs: Dict[str, float], card_group: CardGroup) -> None:
        """
        在对手面对普通牌型选择不出牌时更新概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            card_group (CardGroup): 上家出的牌组
        """
        card_type = card_group.type
        main_rank = card_group.main_rank

        # 根据牌型进行不同的概率更新
        if card_type == CardGroupType.SINGLE:
            # 单牌：降低对手有更大单牌的概率
            self._update_single_card_pass(probs, main_rank)
        elif card_type == CardGroupType.PAIR:
            # 对子：降低对手有更大对子的概率
            self._update_pair_pass(probs, main_rank)
        elif card_type == CardGroupType.TRIO or card_type == CardGroupType.TRIO_WITH_SINGLE or card_type == CardGroupType.TRIO_WITH_PAIR:
            # 三张及其变种：降低对手有更大三张的概率
            self._update_trio_pass(probs, main_rank)
        elif card_type == CardGroupType.STRAIGHT:
            # 顺子：降低对手有相同长度更大顺子的概率
            self._update_straight_pass(probs, main_rank, len(card_group.cards))
        elif card_type == CardGroupType.STRAIGHT_PAIR:
            # 连对：降低对手有相同长度更大连对的概率
            self._update_straight_pair_pass(probs, main_rank, len(card_group.cards) // 2)

    def _update_single_card_pass(self, probs: Dict[str, float], rank: CardRank) -> None:
        """
        更新对手在面对单牌时选择不出牌的概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            rank (CardRank): 上家出的单牌点数
        """
        # 对于比当前单牌大的牌，降低概率
        for r in range(int(rank) + 1, int(CardRank.BIG_JOKER) + 1):
            for suit in range(int(CardSuit.CLUB), int(CardSuit.SPADE) + 1):
                try:
                    card_str = str(Card(CardRank(r), CardSuit(suit)))
                    if card_str in probs:
                        probs[card_str] *= 0.7  # 适度降低概率
                except Exception as e:
                    # 处理王牌特殊情况
                    if r >= int(CardRank.SMALL_JOKER):
                        if r == int(CardRank.SMALL_JOKER):
                            card_str = str(Card(CardRank.SMALL_JOKER))
                        else:
                            card_str = str(Card(CardRank.BIG_JOKER))

                        if card_str in probs:
                            probs[card_str] *= 0.7

    def _update_pair_pass(self, probs: Dict[str, float], rank: CardRank) -> None:
        """
        更新对手在面对对子时选择不出牌的概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            rank (CardRank): 上家出的对子点数
        """
        # 对于比当前对子大的牌，降低概率
        for r in range(int(rank) + 1, int(CardRank.TWO) + 1):  # 对子最大到2
            pair_count = 0
            pair_cards = []

            # 检查每种花色
            for suit in range(int(CardSuit.CLUB), int(CardSuit.SPADE) + 1):
                card_str = str(Card(CardRank(r), CardSuit(suit)))
                if card_str in probs and probs[card_str] > 0.3:  # 只考虑概率较高的牌
                    pair_count += 1
                    pair_cards.append(card_str)

            # 如果有可能组成对子，降低这些牌的概率
            if pair_count >= 2:
                for card in pair_cards:
                    probs[card] *= 0.6

    def _update_trio_pass(self, probs: Dict[str, float], rank: CardRank) -> None:
        """
        更新对手在面对三张时选择不出牌的概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            rank (CardRank): 上家出的三张点数
        """
        # 对于比当前三张大的牌，降低概率
        for r in range(int(rank) + 1, int(CardRank.TWO) + 1):  # 三张最大到2
            trio_count = 0
            trio_cards = []

            # 检查每种花色
            for suit in range(int(CardSuit.CLUB), int(CardSuit.SPADE) + 1):
                card_str = str(Card(CardRank(r), CardSuit(suit)))
                if card_str in probs and probs[card_str] > 0.3:  # 只考虑概率较高的牌
                    trio_count += 1
                    trio_cards.append(card_str)

            # 如果有可能组成三张，降低这些牌的概率
            if trio_count >= 3:
                for card in trio_cards:
                    probs[card] *= 0.5

    def _update_straight_pass(self, probs: Dict[str, float], rank: CardRank, length: int) -> None:
        """
        更新对手在面对顺子时选择不出牌的概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            rank (CardRank): 上家出的顺子最大点数
            length (int): 顺子长度
        """
        # 顺子最大到A，不包括2和王
        max_start_rank = min(int(CardRank.ACE) - length + 1, int(rank) + 1)

        # 对于可能组成更大顺子的牌，适度降低概率
        for start_rank in range(int(rank) - length + 2, max_start_rank + 1):
            straight_cards = []

            # 检查是否有足够的牌组成顺子
            for r in range(start_rank, start_rank + length):
                rank_cards = []
                for suit in range(int(CardSuit.CLUB), int(CardSuit.SPADE) + 1):
                    try:
                        card_str = str(Card(CardRank(r), CardSuit(suit)))
                        if card_str in probs and probs[card_str] > 0.3:
                            rank_cards.append(card_str)
                    except Exception as e:
                        pass  # 忽略无效的牌

                if not rank_cards:  # 如果某个点数没有牌，则不可能组成顺子
                    break

                straight_cards.extend(rank_cards)

            # 降低这些牌的概率
            for card in straight_cards:
                probs[card] *= 0.8

    def _update_straight_pair_pass(self, probs: Dict[str, float], rank: CardRank, length: int) -> None:
        """
        更新对手在面对连对时选择不出牌的概率

        Args:
            probs (Dict[str, float]): 当前概率分布
            rank (CardRank): 上家出的连对最大点数
            length (int): 连对长度（对数）
        """
        # 连对最大到A，不包括2和王
        max_start_rank = min(int(CardRank.ACE) - length + 1, int(rank) + 1)

        # 对于可能组成更大连对的牌，适度降低概率
        for start_rank in range(int(rank) - length + 2, max_start_rank + 1):
            for r in range(start_rank, start_rank + length):
                pair_cards = []
                for suit in range(int(CardSuit.CLUB), int(CardSuit.SPADE) + 1):
                    try:
                        card_str = str(Card(CardRank(r), CardSuit(suit)))
                        if card_str in probs and probs[card_str] > 0.3:
                            pair_cards.append(card_str)
                    except Exception as e:
                        pass  # 忽略无效的牌

                # 降低这些牌的概率
                for card in pair_cards:
                    probs[card] *= 0.7

    def get_belief_state(self) -> BeliefState:
        """
        获取当前信念状态

        Returns:
            BeliefState: 信念状态对象
        """
        return self.belief
