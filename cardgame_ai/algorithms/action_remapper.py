"""
动作ID重映射模块

解决神经网络训练时的动作索引问题
将大范围的动作ID映射到连续的小范围索引
"""
from cardgame_ai.utils.logging import get_logger
# import logging  # 保留以备向后兼容
from typing import Dict, Optional, Tuple

logger = get_logger(__name__)

class ActionRemapper:
    """
    动作ID重映射器
    
    将游戏逻辑使用的动作ID（可能是大数字）映射到
    神经网络使用的连续索引（从0开始）
    """
    
    def __init__(self, max_actions: int = None):
        """
        初始化动作重映射器
        
        Args:
            max_actions: 最大动作数（如果为None，使用256维压缩空间）
        """
        if max_actions is None:
            # Story 2.1: 使用256维压缩动作空间
            max_actions = 256
        self.max_actions = max_actions
        self.action_id_to_index: Dict[int, int] = {}
        self.index_to_action_id: Dict[int, int] = {}
        self.next_index = 0
        
        # 预先注册常见的动作ID范围
        self._register_action_ranges()
        
    def _register_action_ranges(self):
        """预先注册动作ID范围，确保映射的稳定性"""
        # Story 2.1: 使用紧凑的256维动作空间
        # 基于 action_mapping_v2.py 的设计
        
        # 游戏动作: 0-197 (198个实际游戏动作)
        for i in range(0, 198):
            if self.next_index < self.max_actions:
                self.register_action_id(i)
        
        # 预留空间: 198-255 (58个预留位置)
        # 不预先注册，按需动态分配
        
        logger.info(f"动作重映射器初始化完成，注册了 {self.next_index} 个动作 (最大: {self.max_actions})")
        
    def register_action_id(self, action_id: int) -> int:
        """
        注册一个动作ID并返回其索引
        
        Args:
            action_id: 游戏逻辑使用的动作ID
            
        Returns:
            神经网络使用的索引
        """
        if action_id in self.action_id_to_index:
            return self.action_id_to_index[action_id]
            
        if self.next_index >= self.max_actions:
            raise ValueError(f"动作数超过最大限制: {self.max_actions}")
            
        index = self.next_index
        self.action_id_to_index[action_id] = index
        self.index_to_action_id[index] = action_id
        self.next_index += 1
        
        return index
        
    def action_id_to_nn_index(self, action_id: int) -> int:
        """
        将动作ID转换为神经网络索引
        
        Args:
            action_id: 游戏逻辑使用的动作ID
            
        Returns:
            神经网络使用的索引
        """
        if action_id not in self.action_id_to_index:
            logger.warning(f"未注册的动作ID: {action_id}，动态注册")
            return self.register_action_id(action_id)
            
        return self.action_id_to_index[action_id]
        
    def nn_index_to_action_id(self, index: int) -> int:
        """
        将神经网络索引转换回动作ID
        
        Args:
            index: 神经网络使用的索引
            
        Returns:
            游戏逻辑使用的动作ID
        """
        if index not in self.index_to_action_id:
            raise ValueError(f"无效的神经网络索引: {index}")
            
        return self.index_to_action_id[index]
        
    def get_action_space_size(self) -> int:
        """
        获取动作空间大小（用于神经网络）
        
        Returns:
            当前注册的动作数量
        """
        return self.next_index
        
    def remap_batch(self, action_ids: list) -> list:
        """
        批量重映射动作ID
        
        Args:
            action_ids: 动作ID列表
            
        Returns:
            神经网络索引列表
        """
        return [self.action_id_to_nn_index(aid) for aid in action_ids]
        
    def reverse_remap_batch(self, indices: list) -> list:
        """
        批量反向重映射
        
        Args:
            indices: 神经网络索引列表
            
        Returns:
            动作ID列表
        """
        return [self.nn_index_to_action_id(idx) for idx in indices]

# 全局实例
_global_remapper = ActionRemapper()

def get_global_action_remapper() -> ActionRemapper:
    """获取全局动作重映射器实例"""
    return _global_remapper

def action_id_to_nn_index(action_id: int) -> int:
    """便捷函数：将动作ID转换为神经网络索引"""
    return _global_remapper.action_id_to_nn_index(action_id)

def nn_index_to_action_id(index: int) -> int:
    """便捷函数：将神经网络索引转换回动作ID"""
    return _global_remapper.nn_index_to_action_id(index)

def get_nn_action_space_size() -> int:
    """便捷函数：获取神经网络使用的动作空间大小"""
    return _global_remapper.get_action_space_size()