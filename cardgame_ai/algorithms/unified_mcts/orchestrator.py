"""
MCTS编排器模块

MCTSOrchestrator是统一MCTS架构的核心组件，负责协调所有子组件的工作。
它提供统一的MCTS搜索接口，解决了原有6个分散实现的架构混乱问题。

核心功能：
- 统一的MCTS搜索入口点
- 组件协调和生命周期管理
- 插件系统集成
- 配置管理和验证
- 详细的日志和监控
- 向后兼容性支持

作者: Full Stack Dev (James)
创建时间: 2024-12-19
版本: 1.0.0
"""

from typing import Dict, List, Any, Optional, Union, Tuple
import time
from cardgame_ai.utils.logging import get_logger
import logging  # 保留以备向后兼容
import uuid
from pathlib import Path

from .config import MCTSConfig
from .exceptions import (
    MCTSError, MCTSConfigError, MCTSSearchError, MCTSPluginError,
    create_search_error
)
from .results import SearchResult, ActionInfo, ExplanationData, SearchMetrics
from .plugins import <PERSON>luginManager, PluginContext, PluginResult
from .search_engine import SearchEngine, create_search_engine
from .node_manager import NodeManager, create_node_manager
from .action_processor import ActionProcessor, create_action_processor

# AI共生者增强：导入MCTS日志包装器
try:
    from cardgame_ai.utils.mcts_logging_wrapper import MCTSLoggingWrapper, wrap_mcts_instance
    from cardgame_ai.utils.logging_hooks import LoggingHooks
except ImportError:
    MCTSLoggingWrapper = None
    wrap_mcts_instance = None
    LoggingHooks = None

logger = get_logger(__name__)

class MCTSOrchestrator:
    """
    MCTS编排器
    
    统一MCTS架构的核心控制器，负责协调搜索引擎、节点管理器、
    动作处理器和插件系统的工作。
    
    这是解决原有架构混乱问题的关键组件，提供了统一的接口
    来替代6个分散的MCTS实现。
    """
    
    def __init__(self, config: MCTSConfig, 
                 logging_hooks: Optional['LoggingHooks'] = None,
                 enable_logging: bool = False):
        """
        初始化MCTS编排器
        
        AI共生者增强：支持MCTS搜索日志记录
        
        Args:
            config: MCTS配置对象
            logging_hooks: 日志钩子实例（可选）
            enable_logging: 是否启用MCTS搜索日志记录
            
        Raises:
            MCTSConfigError: 当配置无效时
            MCTSError: 当初始化失败时
        """
        self.config = config
        self.orchestrator_id = str(uuid.uuid4())
        self.logger = get_logger(f"{__name__}.{self.orchestrator_id[:8]}")
        
        # AI共生者增强：日志配置
        self.logging_hooks = logging_hooks
        self.enable_logging = enable_logging
        self._wrapped_instance = None
        
        # 验证配置
        try:
            self.config.validate()
        except Exception as e:
            raise MCTSConfigError(f"配置验证失败: {e}", cause=e)
        
        # 初始化状态
        self.initialized = False
        self.active = False
        self.search_count = 0
        self.total_search_time = 0.0
        
        # 初始化组件
        self._initialize_components()
        
        # 初始化插件系统
        self._initialize_plugins()
        
        # 设置日志
        self._setup_logging()
        
        # AI共生者增强：如果启用日志，创建包装器
        if self.enable_logging and MCTSLoggingWrapper is not None:
            self._wrapped_instance = wrap_mcts_instance(
                self,
                self.logging_hooks,
                log_every_n_searches=getattr(self.config, 'log_every_n_searches', 10),
                log_search_details=True
            )
            self.logger.info("已为MCTS编排器启用搜索日志记录")
        
        self.initialized = True
        self.logger.info(f"MCTS编排器初始化完成，ID: {self.orchestrator_id}")
    
    def _initialize_components(self) -> None:
        """初始化核心组件"""
        try:
            # 创建搜索引擎
            self.search_engine = create_search_engine(
                self.config.search_engine_type,
                self.config.search_engine
            )
            self.logger.info(f"搜索引擎已创建: {self.config.search_engine_type}")
            
            # 创建节点管理器
            self.node_manager = create_node_manager(
                self.config.node_manager_type,
                self.config.node_manager
            )
            self.logger.info(f"节点管理器已创建: {self.config.node_manager_type}")
            
            # 创建动作处理器
            self.action_processor = create_action_processor(
                self.config.action_processor_type,
                self.config.action_processor
            )
            self.logger.info(f"动作处理器已创建: {self.config.action_processor_type}")
            
        except Exception as e:
            raise MCTSError(f"组件初始化失败: {e}", cause=e)
    
    def _initialize_plugins(self) -> None:
        """初始化插件系统"""
        try:
            self.plugin_manager = PluginManager()
            
            # 根据配置加载插件
            plugin_config = self.config.plugins
            
            if plugin_config.enable_belief_state:
                from .plugins.node import BeliefStatePlugin
                belief_plugin = BeliefStatePlugin({
                    'weight': plugin_config.belief_state_weight
                })
                self.plugin_manager.register_plugin(belief_plugin)
                self.logger.info("信念状态插件已加载")
            
            if plugin_config.enable_information_value:
                from .plugins.node import InformationValuePlugin
                info_plugin = InformationValuePlugin({
                    'weight': plugin_config.information_value_weight,
                    'method': plugin_config.information_value_method
                })
                self.plugin_manager.register_plugin(info_plugin)
                self.logger.info("信息价值插件已加载")
            
            if plugin_config.enable_gto_exploitation:
                from .plugins.node import GTOExploitationPlugin
                gto_plugin = GTOExploitationPlugin({
                    'strength': plugin_config.gto_exploitation_strength,
                    'threshold': plugin_config.gto_deviation_threshold
                })
                self.plugin_manager.register_plugin(gto_plugin)
                self.logger.info("GTO剥削插件已加载")
            
            if plugin_config.enable_parallel_search:
                from .plugins.search import ParallelSearchPlugin
                parallel_plugin = ParallelSearchPlugin({
                    'threads': plugin_config.parallel_threads,
                    'batch_size': plugin_config.parallel_batch_size
                })
                self.plugin_manager.register_plugin(parallel_plugin)
                self.logger.info("并行搜索插件已加载")
            
            # 始终加载统一动作映射插件（解决单子节点链问题）
            from .plugins.action import UnifiedActionMapperPlugin
            mapper_plugin = UnifiedActionMapperPlugin(
                self.config.action_processor.action_id_ranges
            )
            self.plugin_manager.register_plugin(mapper_plugin)
            self.logger.info("统一动作映射插件已加载")
            
        except Exception as e:
            raise MCTSPluginError(f"插件系统初始化失败: {e}", cause=e)
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        log_config = self.config.logging
        
        if log_config.enable_logging:
            # 设置日志级别
            self.logger.setLevel(getattr(logging, log_config.log_level.upper()))
            
            # 创建日志目录
            if log_config.log_to_file:
                log_dir = Path(log_config.log_dir)
                log_dir.mkdir(parents=True, exist_ok=True)
                
                # 配置文件处理器
                log_file = log_dir / f"{log_config.log_file_prefix}_{self.orchestrator_id[:8]}.log"
                # 🔧 修复：添加UTF-8编码支持，解决中文乱码问题
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(getattr(logging, log_config.log_level.upper()))

                # 设置格式
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
                
                self.logger.info(f"日志文件已创建: {log_file}")
    
    def search(
        self,
        state: Any,
        model: Any,
        temperature: float = 1.0,
        actions_mask: Optional[List[int]] = None,
        belief_trackers: Optional[Dict[str, Any]] = None,
        opponent_model_priors: Optional[Dict[str, Dict[int, float]]] = None,
        deepbelief_tracker: Optional[Dict[str, Any]] = None,
        explain: bool = False,
        max_time_ms: Optional[int] = None,
        **kwargs
    ) -> Union[SearchResult, Tuple[Dict[int, int], Dict[int, float]]]:
        """
        执行MCTS搜索
        
        这是统一MCTS接口的核心方法，替代了原有6个分散实现的接口。
        
        Args:
            state: 根状态，可以是游戏状态或表示网络输出的隐藏状态
            model: 用于预测的模型（表示、动态和预测网络）
            temperature: 温度参数，用于控制探索
            actions_mask: 合法动作掩码
            belief_trackers: 信念追踪器字典，键为玩家ID
            opponent_model_priors: 对手模型先验，键为玩家ID
            deepbelief_tracker: 深度信念追踪器字典
            explain: 是否返回解释数据
            max_time_ms: 最大搜索时间（毫秒）
            **kwargs: 其他参数
            
        Returns:
            SearchResult: 统一的搜索结果对象
            或者 Tuple[Dict[int, int], Dict[int, float]]: 兼容原有接口的返回格式
            
        Raises:
            MCTSSearchError: 当搜索过程中出现错误时
        """
        if not self.initialized:
            raise MCTSError("MCTS编排器未初始化")
        
        search_id = str(uuid.uuid4())
        start_time = time.time()
        
        self.logger.info(f"开始MCTS搜索，ID: {search_id}")
        
        try:
            # 创建搜索上下文
            context = self._create_search_context(
                state, model, temperature, actions_mask,
                belief_trackers, opponent_model_priors, deepbelief_tracker,
                max_time_ms, search_id, **kwargs
            )
            
            # 执行搜索前插件钩子
            self._execute_plugin_hook('pre_search', context)
            
            # 处理合法动作
            legal_actions = self._process_legal_actions(context)
            context.legal_actions = legal_actions
            
            # 创建根节点
            root_node = self._create_root_node(context)
            context.root_node = root_node
            
            # 执行核心搜索
            search_result = self._execute_core_search(context)
            
            # 执行搜索后插件钩子
            self._execute_plugin_hook('post_search', context)
            
            # 创建解释数据
            explanation = None
            if explain or self.config.enable_explanation:
                explanation = self._create_explanation_data(context, search_result)
            
            # 创建最终结果
            final_result = self._create_final_result(
                search_result, explanation, start_time, search_id
            )
            
            # 更新统计信息
            self._update_statistics(time.time() - start_time)
            
            self.logger.info(f"MCTS搜索完成，ID: {search_id}, 耗时: {final_result.search_time:.3f}s")
            
            # 根据参数决定返回格式
            if kwargs.get('legacy_format', False):
                # 返回兼容原有接口的格式
                if explain:
                    # 当explain=True时，返回3个值：visit_counts, action_probabilities, explanation
                    explanation_dict = explanation.to_dict() if explanation else {}
                    return final_result.visit_counts, final_result.action_probabilities, explanation_dict
                else:
                    # 当explain=False时，返回2个值：visit_counts, action_probabilities
                    return final_result.visit_counts, final_result.action_probabilities
            else:
                return final_result
                
        except Exception as e:
            self.logger.error(f"MCTS搜索失败，ID: {search_id}: {e}")
            raise create_search_error(
                f"搜索失败: {e}",
                search_time=time.time() - start_time
            )
    
    def _create_search_context(
        self,
        state: Any,
        model: Any,
        temperature: float,
        actions_mask: Optional[List[int]],
        belief_trackers: Optional[Dict[str, Any]],
        opponent_model_priors: Optional[Dict[str, Dict[int, float]]],
        deepbelief_tracker: Optional[Dict[str, Any]],
        max_time_ms: Optional[int],
        search_id: str,
        **kwargs
    ) -> PluginContext:
        """创建搜索上下文"""
        context = PluginContext(
            state=state,
            model=model,
            config={
                'temperature': temperature,
                'actions_mask': actions_mask,
                'belief_trackers': belief_trackers,
                'opponent_model_priors': opponent_model_priors,
                'deepbelief_tracker': deepbelief_tracker,
                'max_time_ms': max_time_ms,
                'search_id': search_id,
                **kwargs
            },
            total_simulations=self.config.search_engine.num_simulations
        )
        
        return context
    
    def _process_legal_actions(self, context: PluginContext) -> List[Any]:
        """处理合法动作"""
        try:
            # 验证状态对象
            self._validate_state_object(context.state)

            # 使用动作处理器处理合法动作
            action_infos = self.action_processor.process_legal_actions(
                context.state, context.config.get('actions_mask')
            )

            # 🔍 添加详细调试信息
            self.logger.info(f"🔍 Orchestrator调试：动作处理器返回结果")
            self.logger.debug(f"  - action_infos类型: {type(action_infos)}")
            self.logger.info(f"  - action_infos长度: {len(action_infos) if action_infos else 'None'}")
            if action_infos:
                self.logger.debug(f"  - 第一个元素类型: {type(action_infos[0])}")
                self.logger.debug(f"  - 第一个元素内容: {action_infos[0]}")

            # 检查动作处理结果
            if not action_infos:
                self.logger.error("❌ 动作处理器返回空的动作列表")
                # 🔧 修复：不使用紧急降级策略，直接抛出异常
                # 用户明确要求不允许备用降级策略
                raise MCTSSearchError(
                    f"无法获取合法动作，状态类型: {type(context.state).__name__}，"
                    f"状态对象: {context.state}"
                )

            # 检查动作多样性（解决单子节点链问题）
            unique_action_ids = set(info.action_id for info in action_infos)
            if len(unique_action_ids) <= 1:
                # 动作处理器应该已经处理了单一动作问题，如果仍然只有一个动作，
                # 这可能是一个极端的边缘情况，我们记录警告但不抛出错误
                self.logger.warning(
                    f"检测到单一动作问题: 只有{len(unique_action_ids)}个唯一动作，"
                    f"但动作处理器应该已经修复了这个问题"
                )
                # 不抛出错误，让搜索继续进行

            self.logger.debug(f"处理了{len(action_infos)}个动作，{len(unique_action_ids)}个唯一ID")
            return [info.action for info in action_infos]

        except Exception as e:
            self.logger.error(f"动作处理失败: {e}")
            # 🔧 关键修复：移除紧急降级机制，直接抛出异常
            # 用户明确要求不允许降级策略，保持高质量标准
            raise MCTSSearchError(
                f"动作处理失败，无法获取合法动作: {e}，"
                f"状态类型: {type(context.state).__name__}，"
                f"状态详情: {context.state}"
            ) from e

    def _validate_state_object(self, state: Any) -> None:
        """
        验证状态对象的完整性

        Args:
            state: 游戏状态对象

        Raises:
            MCTSSearchError: 当状态对象无效时
        """
        try:
            if state is None:
                raise MCTSSearchError("状态对象为None")

            state_type = type(state).__name__
            self.logger.debug(f"验证状态对象类型: {state_type}")

            # 检查状态对象是否有基本的方法或属性
            has_legal_actions = (
                hasattr(state, 'get_legal_actions') or
                hasattr(state, 'legal_actions')
            )

            if not has_legal_actions:
                self.logger.warning(f"状态对象{state_type}缺少get_legal_actions方法或legal_actions属性")

            # 如果是斗地主状态，检查关键属性
            if hasattr(state, 'game_phase'):
                game_phase = getattr(state, 'game_phase', None)
                self.logger.debug(f"游戏阶段: {game_phase}")

                if hasattr(state, 'current_player'):
                    current_player = getattr(state, 'current_player', None)
                    self.logger.debug(f"当前玩家: {current_player}")

        except Exception as e:
            self.logger.error(f"状态验证失败: {e}")
            # 不抛出异常，让搜索继续进行

    def _generate_emergency_actions(self, state: Any) -> List[Any]:
        """
        生成紧急动作

        当正常的动作获取失败时，生成一些基本的紧急动作

        Args:
            state: 游戏状态对象

        Returns:
            List[Any]: 紧急动作列表
        """
        try:
            # 导入动作类型
            from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
            from cardgame_ai.games.doudizhu.card_group import CardGroup

            emergency_actions = []

            # 添加基本的Pass动作
            emergency_actions.extend([
                None,  # 通用Pass动作
                CardGroup([]),  # 空牌组（不出）
                BidAction.PASS,  # 不叫
                GrabAction.PASS,  # 不抢
            ])

            self.logger.warning(f"生成{len(emergency_actions)}个紧急动作")
            return emergency_actions

        except Exception as e:
            self.logger.error(f"生成紧急动作失败: {e}")
            # 最后的降级策略
            return [None]

    def _create_root_node(self, context: PluginContext) -> Any:
        """创建根节点"""
        try:
            root_node = self.node_manager.create_root_node(
                context.state, context.legal_actions
            )
            
            # 执行节点创建插件钩子
            node_context = PluginContext(
                state=context.state,
                current_node=root_node,
                legal_actions=context.legal_actions,
                config=context.config
            )
            self._execute_plugin_hook('create_node', node_context)
            
            return root_node
            
        except Exception as e:
            raise MCTSSearchError(f"根节点创建失败: {e}", cause=e)
    
    def _execute_core_search(self, context: PluginContext) -> Dict[str, Any]:
        """执行核心搜索算法"""
        try:
            # 使用搜索引擎执行搜索
            search_result = self.search_engine.execute_search(
                context.root_node,
                context.model,
                context.config
            )
            
            return search_result
            
        except Exception as e:
            raise MCTSSearchError(f"核心搜索失败: {e}", cause=e)
    
    def _execute_plugin_hook(self, hook_name: str, context: PluginContext) -> None:
        """执行插件钩子"""
        try:
            self.plugin_manager.execute_plugins(hook_name, context)
        except Exception as e:
            self.logger.warning(f"插件钩子 {hook_name} 执行失败: {e}")
    
    def _create_explanation_data(
        self, context: PluginContext, search_result: Dict[str, Any]
    ) -> ExplanationData:
        """创建解释数据"""
        explanation = ExplanationData(
            search_id=context.config['search_id'],
            active_plugins=[plugin.get_name() for plugin in self.plugin_manager.plugins.values()]
        )
        
        # 添加搜索指标
        if 'metrics' in search_result:
            explanation.search_metrics = search_result['metrics']
        
        return explanation
    
    def _create_final_result(
        self,
        search_result: Dict[str, Any],
        explanation: Optional[ExplanationData],
        start_time: float,
        search_id: str
    ) -> SearchResult:
        """创建最终搜索结果"""
        search_time = time.time() - start_time
        
        result = SearchResult(
            visit_counts=search_result.get('visit_counts', {}),
            action_probabilities=search_result.get('action_probabilities', {}),
            action_infos=search_result.get('action_infos', []),
            search_time=search_time,
            total_simulations=search_result.get('total_simulations', 0),
            tree_size=search_result.get('tree_size', 0),
            explanation=explanation,
            metrics=search_result.get('metrics')
        )
        
        return result
    
    def _update_statistics(self, search_time: float) -> None:
        """更新统计信息"""
        self.search_count += 1
        self.total_search_time += search_time
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取编排器统计信息"""
        avg_search_time = (self.total_search_time / self.search_count 
                          if self.search_count > 0 else 0.0)
        
        return {
            'orchestrator_id': self.orchestrator_id,
            'initialized': self.initialized,
            'active': self.active,
            'search_count': self.search_count,
            'total_search_time': self.total_search_time,
            'average_search_time': avg_search_time,
            'config': self.config.to_dict(),
            'components': {
                'search_engine': self.search_engine.__class__.__name__,
                'node_manager': self.node_manager.__class__.__name__,
                'action_processor': self.action_processor.__class__.__name__
            },
            'plugins': [plugin.get_name() for plugin in self.plugin_manager.plugins.values()]
        }
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            if hasattr(self, 'plugin_manager'):
                self.plugin_manager.cleanup()
            
            if hasattr(self, 'search_engine'):
                self.search_engine.cleanup()
            
            if hasattr(self, 'node_manager'):
                self.node_manager.cleanup()
            
            if hasattr(self, 'action_processor'):
                self.action_processor.cleanup()
            
            self.active = False
            self.logger.info(f"MCTS编排器已清理，ID: {self.orchestrator_id}")
            
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.active = True
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
