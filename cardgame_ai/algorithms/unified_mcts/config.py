"""
统一MCTS配置管理模块

提供完整的MCTS配置管理功能，包括：
- 主配置类MCTSConfig
- 各组件的专用配置类
- 配置验证和默认值管理
- 配置序列化和反序列化
- 配置继承和覆盖机制

支持从字典、YAML、JSON等多种格式加载配置。
所有配置参数都有详细的文档说明和验证规则。

作者: Full Stack Dev (<PERSON>)
创建时间: 2024-12-19
版本: 1.0.0
"""

from typing import Dict, List, Any, Optional, Union, Type
from dataclasses import dataclass, field, asdict
from pathlib import Path
import yaml
import json
from cardgame_ai.utils.logging import get_logger
# import logging  # 保留以备向后兼容
from .exceptions import MCTSConfigError, create_config_error

logger = get_logger(__name__)

@dataclass
class SearchEngineConfig:
    """
    搜索引擎配置类
    
    定义MCTS搜索引擎的所有配置参数，包括基础搜索参数、
    并行搜索配置、优化参数等。
    """
    
    # 基础搜索参数
    num_simulations: int = 450  # Story 1.5统一优化：450次模拟次数
    max_depth: int = 100  # 最大搜索深度
    c_puct: float = 1.4  # UCB探索常数
    discount: float = 0.997  # 折扣因子
    
    # 探索参数
    dirichlet_alpha: float = 0.25  # Dirichlet噪声参数
    exploration_fraction: float = 0.25  # 探索比例
    pb_c_base: int = 19652  # UCB基础常数
    pb_c_init: float = 1.25  # UCB初始常数
    root_exploration_noise: bool = True  # 根节点探索噪声
    
    # 并行搜索配置
    enable_parallel: bool = True  # 启用并行搜索
    num_threads: int = 4  # 并行线程数
    batch_size: int = 16  # 批处理大小
    
    # PPB-MCTS特定配置
    num_workers: int = 8  # PPB-MCTS工作线程数
    pipeline_depth: int = 4  # 流水线深度
    virtual_loss: float = 1.0  # 虚拟损失值
    virtual_loss_decay: float = 0.99  # 虚拟损失衰减
    use_priority_queue: bool = True  # 使用优先队列
    dynamic_batch_size: bool = True  # 动态批量大小
    min_batch_size: int = 8  # 最小批量大小
    max_batch_size: int = 64  # 最大批量大小
    enable_profiling: bool = False  # 启用性能分析
    
    # 优化配置
    enable_ucb_cache: bool = True  # 启用UCB缓存
    enable_node_pool: bool = True  # 启用节点池
    node_pool_size: int = 10000  # 节点池大小
    
    # 超时配置
    max_time_ms: Optional[int] = None  # 最大搜索时间(毫秒)
    enable_timeout: bool = False  # 启用超时检查
    
    def validate(self) -> None:
        """验证搜索引擎配置的有效性"""
        if self.num_simulations <= 0:
            raise create_config_error('num_simulations', self.num_simulations, '必须大于0')
        
        if self.max_depth <= 0:
            raise create_config_error('max_depth', self.max_depth, '必须大于0')
        
        if not 0 < self.c_puct <= 10:
            raise create_config_error('c_puct', self.c_puct, '必须在0和10之间（包含10）')
        
        if not 0 < self.discount <= 1:
            raise create_config_error('discount', self.discount, '必须在0和1之间')
        
        if self.enable_parallel and self.num_threads <= 0:
            raise create_config_error('num_threads', self.num_threads, '启用并行时必须大于0')
        
        if self.batch_size <= 0:
            raise create_config_error('batch_size', self.batch_size, '必须大于0')
        
        # PPB-MCTS配置验证
        if self.num_workers <= 0:
            raise create_config_error('num_workers', self.num_workers, '必须大于0')
        
        if self.pipeline_depth <= 0:
            raise create_config_error('pipeline_depth', self.pipeline_depth, '必须大于0')
        
        if self.virtual_loss < 0:
            raise create_config_error('virtual_loss', self.virtual_loss, '必须大于等于0')
        
        if not 0 < self.virtual_loss_decay <= 1:
            raise create_config_error('virtual_loss_decay', self.virtual_loss_decay, '必须在0和1之间')
        
        if self.dynamic_batch_size:
            if self.min_batch_size <= 0:
                raise create_config_error('min_batch_size', self.min_batch_size, '必须大于0')
            if self.max_batch_size < self.min_batch_size:
                raise create_config_error('max_batch_size', self.max_batch_size, '必须大于等于min_batch_size')

@dataclass
class NodeManagerConfig:
    """
    节点管理器配置类
    
    定义节点创建、管理和优化的相关配置。
    """
    
    # 节点管理配置
    enable_node_pool: bool = True  # 启用节点对象池
    pool_size: int = 10000  # 对象池大小
    enable_node_cache: bool = True  # 启用节点缓存
    cache_size: int = 5000  # 缓存大小
    
    # 内存优化配置
    enable_memory_optimization: bool = True  # 启用内存优化
    gc_threshold: int = 1000  # 垃圾回收阈值
    max_tree_size: int = 50000  # 最大树大小
    
    # 节点扩展配置
    max_children_per_node: int = 200  # 每个节点最大子节点数
    min_visit_count_for_expansion: int = 1  # 扩展所需最小访问次数
    
    def validate(self) -> None:
        """验证节点管理器配置的有效性"""
        if self.enable_node_pool and self.pool_size <= 0:
            raise create_config_error('pool_size', self.pool_size, '启用节点池时必须大于0')
        
        if self.enable_node_cache and self.cache_size <= 0:
            raise create_config_error('cache_size', self.cache_size, '启用缓存时必须大于0')
        
        if self.max_children_per_node <= 0:
            raise create_config_error('max_children_per_node', self.max_children_per_node, '必须大于0')

@dataclass
class ActionProcessorConfig:
    """
    动作处理器配置类
    
    定义动作映射、验证和处理的相关配置。
    这是解决单子节点链问题的关键配置。
    """
    
    # 动作映射配置（修复：使用与实际动作空间一致的565维映射）
    enable_unified_mapping: bool = True  # 启用统一动作映射
    action_id_ranges: Dict[str, tuple] = field(default_factory=lambda: {
        'BidAction': (0, 3),      # 叫分动作: 0-3
        'GrabAction': (4, 5),     # 抢地主动作: 4-5  
        'PassAction': (6, 6),     # 不出动作: 6
        'CardGroup': (7, 564),    # 出牌动作: 7-564 (558个出牌组合)
    })  # 动作ID范围定义 - 符合斗地主565维动作空间
    
    # 动作验证配置
    enable_action_validation: bool = True  # 启用动作验证
    strict_validation: bool = True  # 严格验证模式
    
    # 冲突检测配置
    enable_conflict_detection: bool = True  # 启用冲突检测
    max_conflicts_allowed: int = 0  # 允许的最大冲突数
    
    # 调试配置
    enable_mapping_debug: bool = False  # 启用映射调试
    log_all_mappings: bool = False  # 记录所有映射
    
    def validate(self) -> None:
        """验证动作处理器配置的有效性"""
        if not self.action_id_ranges:
            raise create_config_error('action_id_ranges', self.action_id_ranges, '不能为空')
        
        # 检查ID范围是否重叠
        ranges = list(self.action_id_ranges.values())
        for i, (start1, end1) in enumerate(ranges):
            for j, (start2, end2) in enumerate(ranges[i+1:], i+1):
                if not (end1 < start2 or end2 < start1):
                    raise create_config_error(
                        'action_id_ranges', 
                        self.action_id_ranges, 
                        f'范围重叠: {ranges[i]} 和 {ranges[j]}'
                    )

@dataclass
class PluginConfig:
    """
    插件配置类
    
    定义各种插件的启用状态和参数。
    """
    
    # 信念状态插件
    enable_belief_state: bool = True
    belief_state_weight: float = 0.7
    
    # 信息价值插件
    enable_information_value: bool = True
    information_value_weight: float = 0.3
    information_value_method: str = 'combined'
    
    # GTO剥削插件
    enable_gto_exploitation: bool = False
    gto_exploitation_strength: float = 1.2
    gto_deviation_threshold: float = 0.3
    
    # 并行搜索插件
    enable_parallel_search: bool = True
    parallel_threads: int = 6
    parallel_batch_size: int = 24
    
    def validate(self) -> None:
        """验证插件配置的有效性"""
        if not 0 <= self.belief_state_weight <= 1:
            raise create_config_error('belief_state_weight', self.belief_state_weight, '必须在0和1之间')
        
        if not 0 <= self.information_value_weight <= 1:
            raise create_config_error('information_value_weight', self.information_value_weight, '必须在0和1之间')
        
        if self.information_value_method not in ['combined', 'entropy', 'variance']:
            raise create_config_error('information_value_method', self.information_value_method, '必须是combined、entropy或variance')

@dataclass
class LoggingConfig:
    """
    日志配置类
    
    定义MCTS日志记录的详细配置。
    """
    
    # 基础日志配置
    enable_logging: bool = True
    log_level: str = 'INFO'
    log_to_file: bool = True
    log_to_console: bool = True
    
    # 详细日志配置
    enable_debug: bool = False
    enable_performance_monitoring: bool = True
    enable_search_path_logging: bool = False
    enable_ucb_calculation_logging: bool = False
    
    # 日志文件配置
    log_dir: str = 'logs/mcts'
    log_file_prefix: str = 'unified_mcts'
    max_log_file_size: int = 10 * 1024 * 1024  # 10MB
    max_log_files: int = 5
    
    def validate(self) -> None:
        """验证日志配置的有效性"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.log_level.upper() not in valid_levels:
            raise create_config_error('log_level', self.log_level, f'必须是{valid_levels}之一')

@dataclass
class MCTSConfig:
    """
    主MCTS配置类
    
    整合所有组件的配置，提供统一的配置管理接口。
    """
    
    # 组件类型选择
    search_engine_type: str = 'ppb'       # standard, parallel, optimized, ppb（默认使用PPB-MCTS）
    node_manager_type: str = 'enhanced'    # standard, enhanced
    action_processor_type: str = 'unified' # standard, unified
    
    # 各组件配置
    search_engine: SearchEngineConfig = field(default_factory=SearchEngineConfig)
    node_manager: NodeManagerConfig = field(default_factory=NodeManagerConfig)
    action_processor: ActionProcessorConfig = field(default_factory=ActionProcessorConfig)
    plugins: PluginConfig = field(default_factory=PluginConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # 全局配置
    enable_explanation: bool = False  # 启用解释功能
    enable_metrics_collection: bool = True  # 启用指标收集
    
    def validate(self) -> None:
        """验证整个MCTS配置的有效性"""
        # 验证组件类型
        valid_search_engines = ['standard', 'parallel', 'optimized']
        if self.search_engine_type not in valid_search_engines:
            raise create_config_error('search_engine_type', self.search_engine_type, f'必须是{valid_search_engines}之一')
        
        valid_node_managers = ['standard', 'enhanced']
        if self.node_manager_type not in valid_node_managers:
            raise create_config_error('node_manager_type', self.node_manager_type, f'必须是{valid_node_managers}之一')
        
        valid_action_processors = ['standard', 'unified']
        if self.action_processor_type not in valid_action_processors:
            raise create_config_error('action_processor_type', self.action_processor_type, f'必须是{valid_action_processors}之一')
        
        # 验证各组件配置
        self.search_engine.validate()
        self.node_manager.validate()
        self.action_processor.validate()
        self.plugins.validate()
        self.logging.validate()
        
        logger.info("MCTS配置验证通过")
    
    def copy(self) -> 'MCTSConfig':
        """创建配置的深拷贝"""
        return MCTSConfig(
            search_engine_type=self.search_engine_type,
            node_manager_type=self.node_manager_type,
            action_processor_type=self.action_processor_type,
            search_engine=SearchEngineConfig(**asdict(self.search_engine)),
            node_manager=NodeManagerConfig(**asdict(self.node_manager)),
            action_processor=ActionProcessorConfig(**asdict(self.action_processor)),
            plugins=PluginConfig(**asdict(self.plugins)),
            logging=LoggingConfig(**asdict(self.logging)),
            enable_explanation=self.enable_explanation,
            enable_metrics_collection=self.enable_metrics_collection
        )
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'MCTSConfig':
        """从字典创建配置对象"""
        # 提取各组件配置
        search_engine_config = SearchEngineConfig(**config_dict.get('search_engine', {}))
        node_manager_config = NodeManagerConfig(**config_dict.get('node_manager', {}))
        action_processor_config = ActionProcessorConfig(**config_dict.get('action_processor', {}))
        plugins_config = PluginConfig(**config_dict.get('plugins', {}))
        logging_config = LoggingConfig(**config_dict.get('logging', {}))
        
        # 创建主配置
        config = cls(
            search_engine_type=config_dict.get('search_engine_type', 'optimized'),
            node_manager_type=config_dict.get('node_manager_type', 'enhanced'),
            action_processor_type=config_dict.get('action_processor_type', 'unified'),
            search_engine=search_engine_config,
            node_manager=node_manager_config,
            action_processor=action_processor_config,
            plugins=plugins_config,
            logging=logging_config,
            enable_explanation=config_dict.get('enable_explanation', False),
            enable_metrics_collection=config_dict.get('enable_metrics_collection', True)
        )
        
        config.validate()
        return config
    
    @classmethod
    def from_yaml(cls, yaml_path: Union[str, Path]) -> 'MCTSConfig':
        """从YAML文件加载配置"""
        yaml_path = Path(yaml_path)
        if not yaml_path.exists():
            raise MCTSConfigError(f"配置文件不存在: {yaml_path}")
        
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            return cls.from_dict(config_dict)
        except Exception as e:
            raise MCTSConfigError(f"加载YAML配置失败: {e}", cause=e)
    
    @classmethod
    def from_json(cls, json_path: Union[str, Path]) -> 'MCTSConfig':
        """从JSON文件加载配置"""
        json_path = Path(json_path)
        if not json_path.exists():
            raise MCTSConfigError(f"配置文件不存在: {json_path}")
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            return cls.from_dict(config_dict)
        except Exception as e:
            raise MCTSConfigError(f"加载JSON配置失败: {e}", cause=e)
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return asdict(self)
    
    def save_yaml(self, yaml_path: Union[str, Path]) -> None:
        """保存配置到YAML文件"""
        yaml_path = Path(yaml_path)
        yaml_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(yaml_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)
            logger.info(f"配置已保存到: {yaml_path}")
        except Exception as e:
            raise MCTSConfigError(f"保存YAML配置失败: {e}", cause=e)
    
    def save_json(self, json_path: Union[str, Path]) -> None:
        """保存配置到JSON文件"""
        json_path = Path(json_path)
        json_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {json_path}")
        except Exception as e:
            raise MCTSConfigError(f"保存JSON配置失败: {e}", cause=e)
