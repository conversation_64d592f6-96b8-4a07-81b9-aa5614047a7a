"""
MCTS动作插件模块

提供动作处理相关的插件实现，包括统一动作映射和动作验证。
这些插件是解决单子节点链问题的关键组件。

动作插件类型：
- UnifiedActionMapperPlugin: 统一动作映射插件
- ActionValidatorPlugin: 动作验证插件

作者: Full Stack Dev (James)
创建时间: 2024-12-19
版本: 1.0.0
"""

from typing import Dict, List, Any, Optional, Set, Tuple
from cardgame_ai.utils.logging import get_logger
# import logging  # 保留以备向后兼容
import hashlib
from collections import defaultdict, Counter

from ..base import ActionPlugin, PluginContext, PluginResult
from ...exceptions import MCTSActionMappingError

logger = get_logger(__name__)

class UnifiedActionMapperPlugin(ActionPlugin):
    """
    统一动作映射插件
    
    这是解决单子节点链问题的核心插件。通过统一的动作映射算法，
    确保不同动作映射到不同的ID，避免映射冲突。
    """
    
    def __init__(self, action_id_ranges: Optional[Dict[str, Tuple[int, int]]] = None):
        """
        初始化统一动作映射插件
        
        Args:
            action_id_ranges: 动作ID范围定义
        """
        config = {'action_id_ranges': action_id_ranges or {}}
        super().__init__(config)
        
        # 动作ID范围（修复：使用565维动作空间）
        self.action_id_ranges = action_id_ranges or {
            'BidAction': (0, 3),      # 叫分动作: 0-3
            'GrabAction': (4, 5),     # 抢地主动作: 4-5
            'PassAction': (6, 6),     # 不出动作: 6
            'CardGroup': (7, 564),    # 出牌动作: 7-564 (558个出牌组合)
        }
        
        # 映射缓存和统计
        self.mapping_cache: Dict[str, int] = {}
        self.reverse_mapping: Dict[int, str] = {}
        self.used_ids: Set[int] = set()
        self.mapping_stats = defaultdict(int)
        self.conflict_count = 0
        
        self.logger.info(f"统一动作映射插件初始化，范围: {self.action_id_ranges}")
    
    def get_name(self) -> str:
        return "unified_action_mapper"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "统一动作映射插件，解决单子节点链问题"
    
    def process_actions(self, context: PluginContext) -> PluginResult:
        """
        处理动作列表，确保映射的唯一性
        
        Args:
            context: 插件上下文
            
        Returns:
            PluginResult: 处理结果
        """
        if not context.legal_actions:
            return PluginResult(
                success=False,
                error_message="没有合法动作可处理",
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
        
        # 映射所有动作
        action_mappings = {}
        action_id_counter = Counter()
        conflicts = []
        
        for action in context.legal_actions:
            try:
                action_id = self.map_action_to_id(action)
                action_str = str(action)
                
                action_mappings[action_str] = action_id
                action_id_counter[action_id] += 1
                
                # 检测冲突
                if action_id_counter[action_id] > 1:
                    conflicts.append({
                        'action_id': action_id,
                        'action': action_str,
                        'count': action_id_counter[action_id]
                    })
                
            except Exception as e:
                self.logger.error(f"映射动作失败: {action} -> {e}")
                return PluginResult(
                    success=False,
                    error_message=f"动作映射失败: {e}",
                    plugin_name=self.get_name(),
                    plugin_version=self.get_version()
                )
        
        # 检查动作多样性
        unique_ids = len(set(action_mappings.values()))
        total_actions = len(action_mappings)
        
        if unique_ids < 2 and total_actions > 1:
            return PluginResult(
                success=False,
                error_message=f"检测到单一动作问题: {total_actions}个动作只映射到{unique_ids}个ID",
                data={
                    'conflicts': conflicts,
                    'action_mappings': action_mappings,
                    'unique_ids': unique_ids,
                    'total_actions': total_actions
                },
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
        
        # 更新上下文
        context.set_shared_data('action_mappings', action_mappings)
        context.set_shared_data('mapping_conflicts', conflicts)
        
        self.logger.info(f"动作映射完成: {total_actions}个动作 -> {unique_ids}个唯一ID")
        
        return PluginResult(
            success=True,
            data={
                'action_mappings': action_mappings,
                'unique_ids': unique_ids,
                'total_actions': total_actions,
                'conflicts': conflicts
            },
            plugin_name=self.get_name(),
            plugin_version=self.get_version()
        )
    
    def map_action(self, context: PluginContext) -> PluginResult:
        """
        映射单个动作
        
        Args:
            context: 插件上下文
            
        Returns:
            PluginResult: 映射结果
        """
        action = context.config.get('action')
        if action is None:
            return PluginResult(
                success=False,
                error_message="没有提供要映射的动作",
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
        
        try:
            action_id = self.map_action_to_id(action)
            
            return PluginResult(
                success=True,
                data={
                    'action': str(action),
                    'action_id': action_id,
                    'action_type': self._get_action_type(action)
                },
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
            
        except Exception as e:
            return PluginResult(
                success=False,
                error_message=f"动作映射失败: {e}",
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
    
    def map_action_to_id(self, action: Any) -> int:
        """
        将动作映射为唯一ID
        
        这是解决单子节点链问题的核心方法。
        
        Args:
            action: 动作对象
            
        Returns:
            int: 唯一的动作ID
            
        Raises:
            MCTSActionMappingError: 当映射失败时
        """
        try:
            # 生成动作的字符串表示
            action_str = self._get_action_string(action)
            
            # 检查缓存
            if action_str in self.mapping_cache:
                return self.mapping_cache[action_str]
            
            # 确定动作类型
            action_type = self._get_action_type(action)
            
            # 获取基础范围
            base_range = self.action_id_ranges.get(action_type)
            if base_range is None:
                raise MCTSActionMappingError(f"未知的动作类型: {action_type}")
            
            # 计算动作ID
            action_id = self._calculate_action_id(action, action_type, base_range)
            
            # 检查冲突并解决
            if action_id in self.used_ids:
                action_id = self._resolve_conflict(action, action_id, base_range)
                self.conflict_count += 1
            
            # 记录映射
            self.mapping_cache[action_str] = action_id
            self.reverse_mapping[action_id] = action_str
            self.used_ids.add(action_id)
            self.mapping_stats[action_type] += 1
            
            return action_id
            
        except Exception as e:
            raise MCTSActionMappingError(f"动作映射失败: {e}")
    
    def _get_action_string(self, action: Any) -> str:
        """获取动作的字符串表示"""
        try:
            return str(action) if action is not None else "PassAction"
        except Exception as e:
            return f"Action_{id(action)}"
    
    def _get_action_type(self, action: Any) -> str:
        """获取动作类型"""
        if action is None:
            return 'PassAction'
        
        action_type = type(action).__name__
        
        # 特殊处理
        if 'CardGroup' in action_type or 'Card' in action_type:
            return 'CardGroup'
        elif 'Bid' in action_type:
            return 'BidAction'
        elif 'Grab' in action_type:
            return 'GrabAction'
        elif 'Pass' in action_type:
            return 'PassAction'
        else:
            return action_type
    
    def _calculate_action_id(
        self,
        action: Any,
        action_type: str,
        base_range: Tuple[int, int]
    ) -> int:
        """计算动作ID"""
        start_id, end_id = base_range
        range_size = end_id - start_id + 1
        
        if action_type == 'PassAction' or action is None:
            return start_id
        elif action_type == 'CardGroup':
            return self._map_card_group(action, start_id, range_size)
        elif action_type in ['BidAction', 'GrabAction']:
            return self._map_enum_action(action, start_id, range_size)
        else:
            return self._map_generic_action(action, start_id, range_size)
    
    def _map_card_group(self, card_group: Any, start_id: int, range_size: int) -> int:
        """映射CardGroup动作"""
        try:
            if hasattr(card_group, 'is_pass') and card_group.is_pass():
                return start_id
            
            # 牌型偏移
            type_offset = 0
            if hasattr(card_group, 'card_type'):
                type_map = {
                    'SINGLE': 100, 'PAIR': 200, 'TRIO': 300,
                    'STRAIGHT': 600, 'BOMB': 1300, 'ROCKET': 1400
                }
                card_type_name = str(card_group.card_type).split('.')[-1]
                type_offset = type_map.get(card_type_name, 50)
            
            # 牌组合哈希
            cards_hash = self._compute_cards_hash(card_group)
            
            # 组合最终ID
            action_id = start_id + type_offset + (cards_hash % 100)
            return start_id + (action_id - start_id) % range_size
            
        except Exception:
            return self._map_generic_action(card_group, start_id, range_size)
    
    def _compute_cards_hash(self, card_group: Any) -> int:
        """计算牌组合的哈希值"""
        try:
            if hasattr(card_group, 'cards') and card_group.cards:
                cards_str = ""
                for card in card_group.cards:
                    if hasattr(card, 'rank') and hasattr(card, 'suit'):
                        cards_str += f"{card.rank.value}{card.suit.value}"
                    else:
                        cards_str += str(card)
                
                hash_obj = hashlib.md5(cards_str.encode())
                return int(hash_obj.hexdigest()[:8], 16)
            else:
                return hash(str(card_group)) % 10000
        except Exception:
            return hash(str(card_group)) % 10000
    
    def _map_enum_action(self, action: Any, start_id: int, range_size: int) -> int:
        """映射枚举类型动作"""
        try:
            if hasattr(action, 'value'):
                return start_id + (action.value % range_size)
            else:
                return start_id + (hash(str(action)) % range_size)
        except Exception as e:
            return start_id
    
    def _map_generic_action(self, action: Any, start_id: int, range_size: int) -> int:
        """映射通用动作"""
        try:
            action_str = str(action)
            hash_obj = hashlib.md5(action_str.encode())
            hash_value = int(hash_obj.hexdigest()[:8], 16)
            return start_id + (hash_value % range_size)
        except Exception as e:
            return start_id + (hash(str(action)) % range_size)
    
    def _resolve_conflict(
        self,
        action: Any,
        conflicted_id: int,
        base_range: Tuple[int, int]
    ) -> int:
        """解决映射冲突"""
        start_id, end_id = base_range
        
        # 线性探测
        for offset in range(1, end_id - start_id + 1):
            candidate_id = start_id + ((conflicted_id - start_id + offset) % (end_id - start_id + 1))
            if candidate_id not in self.used_ids:
                return candidate_id
        
        # 扩展范围
        return end_id + 1 + len(self.used_ids)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取映射统计信息"""
        return {
            'total_mappings': len(self.mapping_cache),
            'conflict_count': self.conflict_count,
            'used_ids_count': len(self.used_ids),
            'mapping_stats': dict(self.mapping_stats)
        }

class ActionValidatorPlugin(ActionPlugin):
    """
    动作验证插件
    
    验证动作的合法性和有效性，确保MCTS搜索的正确性。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化动作验证插件"""
        super().__init__(config)
        
        # 验证配置
        self.strict_mode = self.config.get('strict_mode', True)
        self.enable_type_check = self.config.get('enable_type_check', True)
        self.enable_state_check = self.config.get('enable_state_check', True)
        
        # 验证统计
        self.validation_count = 0
        self.validation_failures = 0
        self.failure_reasons = defaultdict(int)
        
        self.logger.info(f"动作验证插件初始化，严格模式: {self.strict_mode}")
    
    def get_name(self) -> str:
        return "action_validator"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return f"动作验证插件，严格模式: {self.strict_mode}"
    
    def validate_action(self, context: PluginContext) -> PluginResult:
        """
        验证单个动作
        
        Args:
            context: 插件上下文
            
        Returns:
            PluginResult: 验证结果
        """
        action = context.config.get('action')
        state = context.state
        
        self.validation_count += 1
        
        try:
            # 基础验证
            if action is None:
                # Pass动作总是有效的
                return PluginResult(
                    success=True,
                    data={'valid': True, 'reason': 'Pass action'},
                    plugin_name=self.get_name(),
                    plugin_version=self.get_version()
                )
            
            # 类型验证
            if self.enable_type_check and not self._validate_action_type(action):
                self.validation_failures += 1
                self.failure_reasons['type_check'] += 1
                return PluginResult(
                    success=True,
                    data={'valid': False, 'reason': 'Invalid action type'},
                    plugin_name=self.get_name(),
                    plugin_version=self.get_version()
                )
            
            # 状态兼容性验证
            if self.enable_state_check and not self._validate_state_compatibility(action, state):
                self.validation_failures += 1
                self.failure_reasons['state_check'] += 1
                return PluginResult(
                    success=True,
                    data={'valid': False, 'reason': 'State incompatible'},
                    plugin_name=self.get_name(),
                    plugin_version=self.get_version()
                )
            
            # 验证通过
            return PluginResult(
                success=True,
                data={'valid': True, 'reason': 'All checks passed'},
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
            
        except Exception as e:
            self.validation_failures += 1
            self.failure_reasons['exception'] += 1
            
            if self.strict_mode:
                return PluginResult(
                    success=True,
                    data={'valid': False, 'reason': f'Validation exception: {e}'},
                    plugin_name=self.get_name(),
                    plugin_version=self.get_version()
                )
            else:
                return PluginResult(
                    success=True,
                    data={'valid': True, 'reason': 'Exception ignored in non-strict mode'},
                    plugin_name=self.get_name(),
                    plugin_version=self.get_version()
                )
    
    def process_actions(self, context: PluginContext) -> PluginResult:
        """
        批量验证动作
        
        Args:
            context: 插件上下文
            
        Returns:
            PluginResult: 验证结果
        """
        if not context.legal_actions:
            return PluginResult(
                success=True,
                data={'valid_actions': [], 'invalid_actions': []},
                plugin_name=self.get_name(),
                plugin_version=self.get_version()
            )
        
        valid_actions = []
        invalid_actions = []
        
        for action in context.legal_actions:
            # 创建临时上下文验证单个动作
            temp_context = PluginContext(
                state=context.state,
                config={'action': action}
            )
            
            result = self.validate_action(temp_context)
            if result.success and result.data.get('valid', False):
                valid_actions.append(action)
            else:
                invalid_actions.append({
                    'action': action,
                    'reason': result.data.get('reason', 'Unknown')
                })
        
        self.logger.info(f"批量验证完成: {len(valid_actions)}个有效, {len(invalid_actions)}个无效")
        
        return PluginResult(
            success=True,
            data={
                'valid_actions': valid_actions,
                'invalid_actions': invalid_actions,
                'total_actions': len(context.legal_actions)
            },
            plugin_name=self.get_name(),
            plugin_version=self.get_version()
        )
    
    def _validate_action_type(self, action: Any) -> bool:
        """验证动作类型"""
        # 基础类型检查
        if action is None:
            return True
        
        # 检查是否有必要的属性或方法
        # 这里可以根据具体的动作类型添加验证逻辑
        return True
    
    def _validate_state_compatibility(self, action: Any, state: Any) -> bool:
        """验证动作与状态的兼容性"""
        # 这里可以添加具体的兼容性验证逻辑
        # 例如检查动作是否在当前状态下合法
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        success_rate = ((self.validation_count - self.validation_failures) / 
                       max(1, self.validation_count))
        
        return {
            'validation_count': self.validation_count,
            'validation_failures': self.validation_failures,
            'success_rate': success_rate,
            'failure_reasons': dict(self.failure_reasons),
            'strict_mode': self.strict_mode
        }

# 导出插件类
__all__ = [
    'UnifiedActionMapperPlugin',
    'ActionValidatorPlugin'
]
