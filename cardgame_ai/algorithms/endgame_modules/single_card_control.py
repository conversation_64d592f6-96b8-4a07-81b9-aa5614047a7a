"""
单张控制模块

提供处理单张控制残局的专门决策逻辑。
"""

from typing import List, Optional, Tuple, Dict, Any, Set

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

def is_single_card_control_scenario(state: State) -> bool:
    """
    检测是否是单张控制残局场景
    
    单张控制残局场景是指游戏接近结束，玩家手中主要是单张牌，且需要通过控制单张牌的出牌顺序来获胜。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是单张控制残局场景
    """
    if not isinstance(state, DouDizhuState):
        return False
    
    # 检查是否是残局（至少有一个玩家手牌数量较少）
    is_endgame = False
    for hand in state.hands:
        if 0 < len(hand) <= 5:
            is_endgame = True
            break
    
    if not is_endgame:
        return False
    
    # 检查当前玩家的手牌
    current_player = state.current_player
    hand = state.hands[current_player]
    
    # 如果手牌数量太少，不考虑单张控制
    if len(hand) <= 1:
        return False
    
    # 统计手牌中的单张、对子、三张等数量
    rank_count = {}
    for card in hand:
        if card.rank not in rank_count:
            rank_count[card.rank] = 0
        rank_count[card.rank] += 1
    
    # 计算单张的数量
    single_count = sum(1 for count in rank_count.values() if count == 1)
    
    # 如果单张数量占手牌的一半以上，则认为是单张控制残局
    return single_count >= len(hand) / 2

def handle_single_card_control(state: State, player_id: int) -> Optional[Action]:
    """
    处理单张控制残局
    
    在单张控制残局中，根据特定规则决定出哪张单牌。
    
    Args:
        state: 游戏状态
        player_id: 玩家ID
        
    Returns:
        Optional[Action]: 决策动作，如果不适用则返回None
    """
    if not isinstance(state, DouDizhuState) or state.current_player != player_id:
        return None
    
    # 检查是否是单张控制残局场景
    if not is_single_card_control_scenario(state):
        return None
    
    # 获取当前玩家的手牌
    hand = state.hands[player_id]
    
    # 统计手牌中的单张、对子、三张等数量
    rank_count = {}
    for card in hand:
        if card.rank not in rank_count:
            rank_count[card.rank] = 0
        rank_count[card.rank] += 1
    
    # 获取所有单张牌
    singles = [card for card in hand if rank_count[card.rank] == 1]
    
    # 如果没有单张牌，返回None
    if not singles:
        return None
    
    # 决策逻辑：
    # 1. 如果是新的一轮出牌（上一个出牌的是当前玩家，或者连续两次不出），选择最小的单张
    # 2. 如果需要跟牌，选择刚好能大过对方的单张
    # 3. 如果没有能大过对方的单张，选择不出
    
    # 如果是新的一轮出牌
    if state.last_player == player_id or state.num_passes >= 2:
        # 选择最小的单张
        smallest_single = min(singles)
        return CardGroup([smallest_single])
    
    # 如果需要跟牌，且上一手是单张
    if state.last_move and state.last_move.card_type == CardGroupType.SINGLE:
        last_card = state.last_move.cards[0]
        
        # 找出所有能大过上一手的单张
        larger_singles = [card for card in singles if card > last_card]
        
        # 如果有能大过上一手的单张，选择最小的一张
        if larger_singles:
            smallest_larger_single = min(larger_singles)
            return CardGroup([smallest_larger_single])
        
        # 如果没有能大过上一手的单张，选择不出
        return CardGroup([])
    
    # 如果上一手不是单张，返回None，让一般决策逻辑处理
    return None

def get_optimal_single_card_sequence(hand: List[Card], opponent_hand: Optional[List[Card]] = None) -> List[Card]:
    """
    获取最优的单张牌出牌顺序
    
    Args:
        hand: 手牌
        opponent_hand: 对手手牌（如果已知）
        
    Returns:
        List[Card]: 最优的单张牌出牌顺序
    """
    # 统计手牌中的单张
    rank_count = {}
    for card in hand:
        if card.rank not in rank_count:
            rank_count[card.rank] = 0
        rank_count[card.rank] += 1
    
    singles = [card for card in hand if rank_count[card.rank] == 1]
    
    # 如果没有单张，返回空列表
    if not singles:
        return []
    
    # 如果不知道对手手牌，按照从小到大的顺序出牌
    if not opponent_hand:
        return sorted(singles)
    
    # 如果知道对手手牌，尝试找出最优的出牌顺序
    # 统计对手手牌中的单张
    opponent_rank_count = {}
    for card in opponent_hand:
        if card.rank not in opponent_rank_count:
            opponent_rank_count[card.rank] = 0
        opponent_rank_count[card.rank] += 1
    
    opponent_singles = [card for card in opponent_hand if opponent_rank_count[card.rank] == 1]
    
    # 如果对手没有单张，按照从小到大的顺序出牌
    if not opponent_singles:
        return sorted(singles)
    
    # 尝试找出能够控制对手的出牌顺序
    # 策略：先出能够卡住对手的牌，然后出较小的牌
    
    # 获取对手单张的点数集合
    opponent_single_ranks = {card.rank for card in opponent_singles}
    
    # 找出能够卡住对手的牌（比对手最大的单张大的牌）
    opponent_max_single = max(opponent_singles)
    control_cards = [card for card in singles if card > opponent_max_single]
    
    # 找出不能被对手卡住的牌（对手没有比这些牌大的单张）
    safe_cards = []
    for card in singles:
        if all(card > opp_card for opp_card in opponent_singles):
            safe_cards.append(card)
    
    # 构建最优出牌顺序：先出控制牌，然后出安全牌，最后出其他牌
    optimal_sequence = []
    
    # 先出控制牌（从大到小）
    optimal_sequence.extend(sorted(control_cards, reverse=True))
    
    # 然后出安全牌（从小到大）
    optimal_sequence.extend(sorted(safe_cards))
    
    # 最后出其他牌（从小到大）
    other_cards = [card for card in singles if card not in control_cards and card not in safe_cards]
    optimal_sequence.extend(sorted(other_cards))
    
    return optimal_sequence
